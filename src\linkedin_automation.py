"""LinkedIn自动化模块
实现LinkedIn登录验证、搜索职位、筛选Easy Apply职位并进行投递的功能
"""

import time
import random
from typing import List, Dict, Optional
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import (
    NoSuchElementException, 
    TimeoutException, 
    ElementClickInterceptedException,
    StaleElementReferenceException
)
from selenium.webdriver.chrome.options import Options
from loguru import logger
import yaml
from pathlib import Path

class LinkedInAutomation:
    """LinkedIn自动化类"""
    
    def __init__(self, config_path: str = None):
        """
        初始化LinkedIn自动化实例
        
        Args:
            config_path: 配置文件路径
        """
        self.driver = None
        self.wait = None
        self.config = self._load_config(config_path)
        self.is_logged_in = False
        self.applied_jobs = set()  # 记录已申请的职位
        
    def _load_config(self, config_path: str) -> Dict:
        """加载配置文件"""
        if config_path and Path(config_path).exists():
            with open(config_path, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)
        
        # 默认配置
        return {
            'linkedin': {
                'email': '',
                'password': '',
                'search_keywords': ['python developer', 'software engineer'],
                'location': 'United States',
                'experience_level': ['Entry level', 'Associate', 'Mid-Senior level'],
                'job_type': ['Full-time', 'Part-time', 'Contract'],
                'remote_work': ['On-site', 'Remote', 'Hybrid'],
                'max_applications_per_day': 50,
                'delay_between_applications': [30, 60],  # 秒
                'auto_answer_questions': True,
                'default_answers': {
                    'years_experience': '3',
                    'willing_to_relocate': 'Yes',
                    'authorized_to_work': 'Yes',
                    'require_sponsorship': 'No'
                }
            },
            'selenium': {
                'headless': False,
                'implicit_wait': 10,
                'page_load_timeout': 30,
                'window_size': [1920, 1080]
            }
        }
    
    def setup_driver(self, headless: bool = None) -> webdriver.Chrome:
        """设置Chrome WebDriver"""
        # 使用优化后的chrome_browser_options函数
        from src.utils.chrome_utils import chrome_browser_options
        chrome_options = chrome_browser_options()
        
        if headless is None:
            headless = self.config['selenium']['headless']
            
        if headless:
            chrome_options.add_argument('--headless')
        
        # 窗口大小
        window_size = self.config['selenium']['window_size']
        chrome_options.add_argument(f'--window-size={window_size[0]},{window_size[1]}')
        
        # 添加额外的反检测设置
        chrome_options.add_argument('--disable-blink-features=AutomationControlled')
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)
        
        try:
            # 使用 webdriver_manager 自动管理 ChromeDriver
            from webdriver_manager.chrome import ChromeDriverManager
            from selenium.webdriver.chrome.service import Service

            service = Service(ChromeDriverManager().install())
            self.driver = webdriver.Chrome(service=service, options=chrome_options)
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            
            # 设置等待和超时
            self.driver.implicitly_wait(self.config['selenium']['implicit_wait'])
            self.driver.set_page_load_timeout(self.config['selenium']['page_load_timeout'])
            
            self.wait = WebDriverWait(self.driver, 20)
            
            logger.info("Chrome WebDriver 设置完成")
            return self.driver
        except Exception as e:
            logger.error(f"设置Chrome WebDriver失败: {str(e)}")
            raise RuntimeError(f"设置Chrome WebDriver失败: {str(e)}")
    
    def login(self, email: str = None, password: str = None) -> Dict:
        """登录LinkedIn"""
        try:
            if not email:
                email = self.config['linkedin']['email']
            if not password:
                password = self.config['linkedin']['password']
                
            if not email or not password:
                logger.error("LinkedIn邮箱或密码未配置")
                return {"success": False, "status": "邮箱或密码未配置", "requires_action": False}
            
            logger.info(f"开始登录LinkedIn... 使用邮箱: {email[:3]}****{email[-10:]}")
            try:
                logger.debug("正在访问LinkedIn登录页面...")
                self.driver.get("https://www.linkedin.com/login")
                logger.debug(f"当前URL: {self.driver.current_url}")
            except Exception as e:
                logger.error(f"无法访问LinkedIn登录页面: {str(e)}")
                return {"success": False, "status": "无法访问LinkedIn登录页面，请检查网络连接或代理设置", "requires_action": False}
            
            # 等待登录页面加载
            try:
                logger.debug("等待登录页面元素加载...")
                email_field = self.wait.until(
                    EC.presence_of_element_located((By.ID, "username"))
                )
                logger.debug("登录页面元素加载成功")
            except TimeoutException:
                logger.error("登录页面加载超时")
                logger.debug(f"当前页面标题: {self.driver.title}")
                logger.debug(f"当前页面URL: {self.driver.current_url}")
                return {"success": False, "status": "登录页面加载超时，请检查网络连接速度", "requires_action": False}
            
            # 输入邮箱
            try:
                logger.debug("正在输入邮箱...")
                email_field.clear()
                self._human_type(email_field, email)
                logger.info("邮箱输入完成")
            except Exception as e:
                logger.error(f"邮箱输入失败: {str(e)}")
                return {"success": False, "status": "邮箱输入失败，请检查邮箱格式是否正确", "requires_action": False}
            
            # 输入密码
            try:
                logger.debug("正在输入密码...")
                password_field = self.driver.find_element(By.ID, "password")
                password_field.clear()
                self._human_type(password_field, password)
                logger.info("密码输入完成")
            except Exception as e:
                logger.error(f"密码输入失败: {str(e)}")
                return {"success": False, "status": "密码输入失败，请检查密码格式是否正确", "requires_action": False}
            
            # 点击登录按钮
            try:
                logger.debug("正在点击登录按钮...")
                login_button = self.driver.find_element(By.XPATH, "//button[@type='submit']")
                login_button.click()
                logger.info("点击登录按钮完成")
            except Exception as e:
                logger.error(f"登录按钮点击失败: {str(e)}")
                return {"success": False, "status": "登录按钮点击失败，请稍后重试", "requires_action": False}
            
            # 等待登录完成，增加等待时间以确保页面加载
            logger.debug("等待登录处理...")
            time.sleep(8)  # 增加等待时间
            
            # 记录当前URL和页面标题，帮助调试
            logger.debug(f"登录后当前URL: {self.driver.current_url}")
            logger.debug(f"登录后页面标题: {self.driver.title}")
            
            # 检查是否需要验证码或其他验证
            if "challenge" in self.driver.current_url or "checkpoint/challenge" in self.driver.current_url:
                logger.warning("检测到验证码验证")
                return {"success": False, "status": "需要完成验证码验证，请在浏览器中完成验证后重试", "requires_action": True}
            
            # 检查是否需要二次验证（checkpoint）
            if "checkpoint" in self.driver.current_url:
                logger.warning("检测到二次验证，等待用户完成验证...")
                max_wait = 120  # 最长等待秒数
                poll_interval = 2  # 检查间隔
                waited = 0
                while waited < max_wait:
                    try:
                        current_url = self.driver.current_url
                        logger.debug(f"二次验证等待中，当前URL: {current_url}")
                        if ("feed" in current_url or "linkedin.com/feed" in current_url or
                            self.driver.find_elements(By.CLASS_NAME, "global-nav") or
                            self.driver.find_elements(By.CLASS_NAME, "feed-identity-module")):
                            logger.info("二次验证后检测到已登录，进入首页")
                            self.is_logged_in = True
                            return {"success": True, "status": "登录成功", "requires_action": False}
                    except Exception as e:
                        logger.warning(f"等待二次验证时页面异常: {str(e)}")
                    time.sleep(poll_interval)
                    waited += poll_interval
                logger.error("二次验证超时，未检测到登录成功")
                return {"success": False, "status": "二次验证超时，请手动检查浏览器中的登录状态", "requires_action": True}
            
            # 检查是否有错误消息
            try:
                error_elements = self.driver.find_elements(By.CLASS_NAME, "alert-content")
                for error_element in error_elements:
                    if error_element.is_displayed():
                        error_text = error_element.text
                        logger.error(f"登录错误: {error_text}")
                        if "密码" in error_text:
                            return {"success": False, "status": "密码错误，请检查密码是否正确", "requires_action": False}
                        elif "找不到" in error_text or "不存在" in error_text:
                            return {"success": False, "status": "账号不存在，请检查邮箱是否正确", "requires_action": False}
                        else:
                            return {"success": False, "status": f"登录错误: {error_text}", "requires_action": False}
            except Exception as e:
                logger.debug(f"检查错误消息时发生异常: {str(e)}")
                pass
            
            # 验证登录状态
            try:
                # 等待页面加载完成
                logger.debug("验证登录状态...")
                try:
                    self.wait.until(
                        EC.presence_of_element_located((By.CLASS_NAME, "global-nav"))
                    )
                    logger.debug("导航栏元素已找到")
                except TimeoutException:
                    logger.debug("未找到导航栏元素，尝试其他元素")
                    # 尝试其他可能的元素
                    try:
                        self.wait.until(
                            EC.presence_of_element_located((By.CLASS_NAME, "feed-identity-module"))
                        )
                        logger.debug("Feed身份模块元素已找到")
                    except TimeoutException:
                        logger.error("无法找到任何登录成功的标志元素")
                        return {"success": False, "status": "登录可能失败，无法找到登录成功的标志元素", "requires_action": False}
                
                # 检查是否在登录页面
                if "login" in self.driver.current_url:
                    logger.error("登录失败：仍在登录页面")
                    return {"success": False, "status": "登录失败，请检查账号密码是否正确", "requires_action": False}
                
                # 检查是否成功加载导航栏
                if not self.driver.find_elements(By.CLASS_NAME, "global-nav") and not self.driver.find_elements(By.CLASS_NAME, "feed-identity-module"):
                    logger.error("登录失败：无法加载导航栏或Feed身份模块")
                    return {"success": False, "status": "登录失败，页面加载异常", "requires_action": False}
                
                self.is_logged_in = True
                logger.info("LinkedIn登录成功")
                return {"success": True, "status": "登录成功", "requires_action": False}
                
            except TimeoutException:
                logger.error("登录超时：无法加载主页")
                return {"success": False, "status": "登录超时，请检查网络连接", "requires_action": False}
            except Exception as e:
                logger.error(f"登录验证失败: {str(e)}")
                return {"success": False, "status": "登录验证失败，请稍后重试", "requires_action": False}
                
        except Exception as e:
            logger.error(f"登录过程发生未知错误: {str(e)}")
            return {"success": False, "status": "登录过程发生未知错误，请稍后重试", "requires_action": False}

    def verify_login_status(self) -> Dict:
        """手动验证登录状态

        Returns:
            包含验证结果的字典
        """
        try:
            logger.info("开始验证登录状态...")

            # 检查当前URL
            current_url = self.driver.current_url
            logger.debug(f"当前URL: {current_url}")

            # 如果在登录页面，说明未登录
            if "login" in current_url:
                logger.info("当前在登录页面，未登录")
                self.is_logged_in = False
                return {"success": False, "status": "未登录", "requires_action": False}

            # 检查是否需要验证
            if "challenge" in current_url or "checkpoint" in current_url:
                logger.warning("检测到需要验证")
                return {"success": False, "status": "需要完成验证，请在浏览器中完成验证", "requires_action": True}

            # 尝试访问LinkedIn主页来验证登录状态
            try:
                logger.debug("访问LinkedIn主页验证登录状态...")
                self.driver.get("https://www.linkedin.com/feed/")
                time.sleep(3)

                # 检查是否被重定向到登录页面
                if "login" in self.driver.current_url:
                    logger.info("被重定向到登录页面，登录已过期")
                    self.is_logged_in = False
                    return {"success": False, "status": "登录已过期，请重新登录", "requires_action": False}

                # 检查是否有导航栏或其他登录标志（增加等待时间）
                try:
                    # 增加等待时间到30秒，等待导航栏加载
                    wait_long = WebDriverWait(self.driver, 30)
                    wait_long.until(
                        EC.any_of(
                            EC.presence_of_element_located((By.CLASS_NAME, "global-nav")),
                            EC.presence_of_element_located((By.CLASS_NAME, "feed-identity-module")),
                            EC.presence_of_element_located((By.CSS_SELECTOR, "[data-control-name='nav.settings']")),
                            EC.presence_of_element_located((By.CSS_SELECTOR, ".nav-item__profile-member-photo")),
                            EC.presence_of_element_located((By.CSS_SELECTOR, ".global-nav__me")),
                            EC.presence_of_element_located((By.CSS_SELECTOR, ".feed-identity"))
                        )
                    )

                    logger.info("登录状态验证成功")
                    self.is_logged_in = True
                    return {"success": True, "status": "已登录", "requires_action": False}

                except TimeoutException:
                    logger.warning("无法找到登录标志元素")
                    # 再次检查URL
                    if "feed" in self.driver.current_url or "linkedin.com" in self.driver.current_url:
                        # 如果在LinkedIn域名下但找不到标志元素，可能是页面加载问题
                        logger.info("在LinkedIn域名下，假设已登录")
                        self.is_logged_in = True
                        return {"success": True, "status": "已登录（部分验证）", "requires_action": False}
                    else:
                        logger.error("登录状态验证失败")
                        self.is_logged_in = False
                        return {"success": False, "status": "登录状态验证失败", "requires_action": False}

            except Exception as e:
                logger.error(f"访问LinkedIn主页失败: {str(e)}")
                return {"success": False, "status": "无法访问LinkedIn，请检查网络连接", "requires_action": False}

        except Exception as e:
            logger.error(f"验证登录状态时发生错误: {str(e)}")
            return {"success": False, "status": "验证登录状态时发生错误", "requires_action": False}

    def search_jobs(self, keywords: str = None, location: str = None,
                   easy_apply_only: bool = True) -> List[Dict]:
        """搜索职位"""
        try:
            if not self.is_logged_in:
                logger.error("请先登录LinkedIn")
                return []

            if not keywords:
                keywords = self.config['linkedin']['search_keywords'][0]
            if not location:
                location = self.config['linkedin']['location']

            logger.info(f"搜索职位: {keywords} in {location}")

            # 构建搜索URL
            search_url = f"https://www.linkedin.com/jobs/search/?keywords={keywords}&location={location}"
            if easy_apply_only:
                search_url += "&f_AL=true"  # Easy Apply筛选

            self.driver.get(search_url)
            time.sleep(5)  # 增加等待时间让页面完全加载

            # 滚动页面以加载更多职位
            self._scroll_to_load_jobs()

            # 保存页面HTML快照到 log 文件夹
            log_dir = Path(__file__).parent.parent / "log"
            log_dir.mkdir(parents=True, exist_ok=True)
            html_file_path = log_dir / "linkedin_jobs_full_page.html"
            with open(html_file_path, "w", encoding="utf-8") as f:
                f.write(self.driver.page_source)
            logger.info(f"页面HTML快照已保存到: {html_file_path}")

            # 使用LLM解析职位信息
            jobs = self._parse_jobs_with_llm(self.driver.page_source)

            if not jobs:
                logger.warning("LLM解析未找到职位，尝试传统方法")
                # 回退到传统解析方法
                jobs = self._extract_jobs_traditional()

            logger.info(f"成功提取 {len(jobs)} 个职位信息")
            return jobs

        except Exception as e:
            logger.error(f"搜索职位失败: {str(e)}", exc_info=True)
            # 保存失败时的页面HTML快照
            log_dir = Path(__file__).parent.parent / "log"
            log_dir.mkdir(parents=True, exist_ok=True)
            error_file_path = log_dir / "linkedin_jobs_error_page.html"
            with open(error_file_path, "w", encoding="utf-8") as f:
                f.write(self.driver.page_source)
            logger.info(f"错误页面快照已保存到: {error_file_path}")
            return []

    def _scroll_to_load_jobs(self):
        """滚动页面以加载更多职位"""
        try:
            logger.info("开始滚动页面加载职位...")
            last_height = self.driver.execute_script("return document.body.scrollHeight")
            scroll_attempts = 0
            max_scrolls = 5

            while scroll_attempts < max_scrolls:
                # 滚动到页面底部
                self.driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
                time.sleep(2)

                # 计算新的滚动高度并与上次的滚动高度进行比较
                new_height = self.driver.execute_script("return document.body.scrollHeight")
                if new_height == last_height:
                    break
                last_height = new_height
                scroll_attempts += 1

            logger.info(f"滚动完成，共滚动 {scroll_attempts} 次")
        except Exception as e:
            logger.warning(f"滚动页面时出错: {str(e)}")

    def _parse_jobs_with_llm(self, page_source: str) -> List[Dict]:
        """使用LLM解析职位信息"""
        try:
            logger.info("开始使用LLM解析职位信息...")

            # 获取API密钥
            api_key = self._get_api_key()
            if not api_key:
                logger.error("未找到API密钥，无法使用LLM解析")
                return []

            # 导入LLM相关模块
            from langchain_google_genai import ChatGoogleGenerativeAI
            from langchain_core.prompts import ChatPromptTemplate
            from langchain_core.output_parsers import StrOutputParser
            import json

            # 初始化LLM
            llm = ChatGoogleGenerativeAI(
                model="gemini-2.5-flash-preview-05-20",
                google_api_key=api_key,
                temperature=1.0,  # 使用用户偏好的温度设置
                request_timeout=60.0,
                max_retries=3,
                transport="rest"
            )

            # 创建解析提示
            prompt_template = ChatPromptTemplate.from_template("""
你是一个专业的LinkedIn职位信息解析专家。请从以下HTML页面源码中提取所有职位信息。

请仔细分析HTML内容，找出所有职位卡片，并提取以下信息：
1. 职位标题 (title)
2. 公司名称 (company)
3. 职位地点 (location)
4. 职位链接 (url) - 完整的LinkedIn职位链接
5. 是否支持Easy Apply (is_easy_apply) - true/false

请以JSON数组格式返回结果，每个职位一个对象。如果某个字段无法找到，请使用合理的默认值。

HTML内容：
{html_content}

请只返回JSON数组，不要包含任何其他文本或解释。格式如下：
[
  {{
    "title": "职位标题",
    "company": "公司名称",
    "location": "职位地点",
    "url": "https://www.linkedin.com/jobs/view/职位ID",
    "is_easy_apply": true,
    "job_id": "职位ID"
  }}
]
""")

            # 截取HTML内容的关键部分以避免token限制
            truncated_html = self._truncate_html_for_llm(page_source)

            # 创建链式处理
            chain = prompt_template | llm | StrOutputParser()

            # 调用LLM
            logger.info("正在调用LLM解析职位信息...")
            response = chain.invoke({"html_content": truncated_html})

            # 解析JSON响应
            try:
                # 清理响应文本，移除可能的markdown标记
                clean_response = response.strip()
                if clean_response.startswith("```json"):
                    clean_response = clean_response[7:]
                if clean_response.endswith("```"):
                    clean_response = clean_response[:-3]
                clean_response = clean_response.strip()

                jobs_data = json.loads(clean_response)

                # 验证和清理数据
                validated_jobs = []
                for job in jobs_data:
                    if isinstance(job, dict) and job.get('title') and job.get('company'):
                        # 确保job_id存在
                        if not job.get('job_id'):
                            job['job_id'] = self._extract_job_id(job.get('url', '')) or str(hash(job.get('title', '') + job.get('company', '')))

                        # 确保URL是完整的LinkedIn链接
                        if job.get('url') and not job['url'].startswith('http'):
                            if job['url'].startswith('/'):
                                job['url'] = 'https://www.linkedin.com' + job['url']

                        validated_jobs.append(job)

                logger.info(f"LLM成功解析出 {len(validated_jobs)} 个职位")
                return validated_jobs

            except json.JSONDecodeError as e:
                logger.error(f"LLM响应JSON解析失败: {str(e)}")
                logger.debug(f"LLM原始响应: {response[:500]}...")
                return []

        except Exception as e:
            logger.error(f"LLM解析职位信息失败: {str(e)}")
            return []

    def _get_api_key(self) -> str:
        """获取API密钥"""
        try:
            # 尝试从多个来源获取API密钥
            import os
            from pathlib import Path
            import yaml

            # 1. 从环境变量获取
            api_key = os.getenv('GEMINI_API_KEY') or os.getenv('GOOGLE_API_KEY')
            if api_key:
                return api_key

            # 2. 从secrets.yaml文件获取
            secrets_file = Path(__file__).parent.parent / "secrets.yaml"
            if secrets_file.exists():
                with open(secrets_file, 'r', encoding='utf-8') as f:
                    secrets = yaml.safe_load(f)
                    api_key = secrets.get('gemini_api_key') or secrets.get('google_api_key') or secrets.get('llm_api_key')
                    if api_key:
                        return api_key

            # 3. 从data_folder/secrets.yaml获取
            data_secrets_file = Path(__file__).parent.parent / "data_folder" / "secrets.yaml"
            if data_secrets_file.exists():
                with open(data_secrets_file, 'r', encoding='utf-8') as f:
                    secrets = yaml.safe_load(f)
                    api_key = secrets.get('gemini_api_key') or secrets.get('google_api_key') or secrets.get('llm_api_key')
                    if api_key:
                        return api_key

            logger.warning("未找到API密钥")
            return None

        except Exception as e:
            logger.error(f"获取API密钥时出错: {str(e)}")
            return None

    def _truncate_html_for_llm(self, html_content: str, max_chars: int = 2000000) -> str:
        """截取HTML内容以适应LLM token限制"""
        if len(html_content) <= max_chars:
            return html_content

        # 尝试找到职位相关的部分
        job_keywords = ['job', 'position', 'career', 'work', 'employment', 'base-search-card', 'job-card']

        # 查找包含职位关键词的部分
        best_start = 0
        best_score = 0

        for i in range(0, len(html_content) - max_chars, 1000):
            chunk = html_content[i:i + max_chars].lower()
            score = sum(chunk.count(keyword) for keyword in job_keywords)
            if score > best_score:
                best_score = score
                best_start = i

        truncated = html_content[best_start:best_start + max_chars]
        logger.info(f"HTML内容已截取: {len(html_content)} -> {len(truncated)} 字符")
        return truncated

    def _extract_jobs_traditional(self) -> List[Dict]:
        """传统方法提取职位信息（作为LLM解析的备用方案）"""
        try:
            logger.info("使用传统方法提取职位信息...")

            # 尝试多种选择器
            selectors = [
                ".base-search-card",
                ".job-card-container",
                ".jobs-search-results__list-item",
                "[data-job-id]",
                ".job-card-list__item"
            ]

            job_cards = []
            for selector in selectors:
                try:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    if elements:
                        job_cards = elements
                        logger.info(f"使用选择器 {selector} 找到 {len(job_cards)} 个职位卡片")
                        break
                except Exception:
                    continue

            if not job_cards:
                logger.warning("未找到任何职位卡片")
                return []

            jobs = []
            for card in job_cards[:20]:  # 限制前20个职位
                try:
                    job_info = self._extract_job_info(card)
                    if job_info:
                        jobs.append(job_info)
                except Exception as e:
                    logger.warning(f"提取职位信息失败: {str(e)}")
                    continue

            logger.info(f"传统方法成功提取 {len(jobs)} 个职位")
            return jobs

        except Exception as e:
            logger.error(f"传统方法提取职位失败: {str(e)}")
            return []

    def _extract_job_info(self, job_card) -> Optional[Dict]:
        """从职位卡片提取信息"""
        try:
            # 捕获职位卡片的HTML内容
            logger.debug(f"职位卡片HTML: {job_card.get_attribute('outerHTML')}")

            # 职位标题
            title_element = job_card.find_element(
                By.CSS_SELECTOR, 
                ".job-card-list__title, .jobs-unified-top-card__job-title a, h3 a"
            )
            title = title_element.text.strip()
            job_url = title_element.get_attribute('href')

            # 公司名称
            company_element = job_card.find_element(
                By.CSS_SELECTOR,
                ".job-card-container__company-name, .jobs-unified-top-card__company-name a, .job-card-container__primary-description"
            )
            company = company_element.text.strip()

            # 地点
            try:
                location_element = job_card.find_element(
                    By.CSS_SELECTOR,
                    ".job-card-container__metadata-item, .jobs-unified-top-card__bullet"
                )
                location = location_element.text.strip()
            except:
                location = "未知地点"

            # 检查是否为Easy Apply - 使用多种检测方法
            is_easy_apply = False

            # 方法1: 检查卡片HTML内容
            try:
                card_html = job_card.get_attribute('outerHTML')
                easy_apply_indicators = [
                    "Easy Apply", "轻松申请", "抢先申请", "正在招聘",
                    "job-posting-benefits", "Apply now", "easy-apply"
                ]
                for indicator in easy_apply_indicators:
                    if indicator in card_html:
                        is_easy_apply = True
                        break
            except:
                pass

            # 方法2: 查找Easy Apply相关元素
            if not is_easy_apply:
                easy_apply_selectors = [
                    ".jobs-apply-button--top-card",
                    ".job-card-container__apply-method",
                    ".job-posting-benefits",
                    ".jobs-s-apply",
                    "[data-control-name*='easy_apply']",
                    "[data-control-name*='job_card_easy_apply']",
                    ".easy-apply-button",
                    "button[aria-label*='Easy Apply']"
                ]

                for selector in easy_apply_selectors:
                    try:
                        easy_apply_element = job_card.find_element(By.CSS_SELECTOR, selector)
                        if easy_apply_element and easy_apply_element.is_displayed():
                            element_text = easy_apply_element.text
                            if ("Easy Apply" in element_text or "轻松申请" in element_text or
                                "抢先申请" in element_text or element_text == ""):
                                is_easy_apply = True
                                break
                    except:
                        continue

            # 方法3: 查找包含Easy Apply文本的任何元素
            if not is_easy_apply:
                try:
                    easy_apply_text_elements = job_card.find_elements(
                        By.XPATH,
                        ".//*[contains(text(), 'Easy Apply') or contains(text(), '轻松申请') or contains(text(), '抢先申请')]"
                    )
                    if easy_apply_text_elements:
                        is_easy_apply = True
                except:
                    pass

            return {
                'title': title,
                'company': company,
                'location': location,
                'url': job_url,
                'is_easy_apply': is_easy_apply,
                'job_id': self._extract_job_id(job_url)
            }

        except Exception as e:
            logger.warning(f"提取职位信息失败: {str(e)}")
            return None
    
    def _extract_job_id(self, job_url: str) -> str:
        """从URL提取职位ID"""
        try:
            if '/jobs/view/' in job_url:
                return job_url.split('/jobs/view/')[1].split('/')[0]
            return job_url.split('/')[-1].split('?')[0]
        except:
            return str(hash(job_url))
    
    def apply_to_job(self, job_info: Dict) -> bool:
        """申请职位"""
        try:
            job_id = job_info['job_id']
            
            # 检查是否已申请
            if job_id in self.applied_jobs:
                logger.info(f"职位 {job_info['title']} 已申请过，跳过")
                return False
            
            if not job_info['is_easy_apply']:
                logger.info(f"职位 {job_info['title']} 不支持Easy Apply，跳过")
                return False
            
            logger.info(f"开始申请职位: {job_info['title']} at {job_info['company']}")
            logger.info(f"职位URL: {job_info['url']}")
            logger.info(f"Easy Apply状态: {job_info['is_easy_apply']}")

            # 打开职位页面
            self.driver.get(job_info['url'])
            time.sleep(3)  # 增加等待时间确保页面完全加载

            # 等待页面加载完成
            try:
                WebDriverWait(self.driver, 10).until(
                    EC.presence_of_element_located((By.TAG_NAME, "body"))
                )
                logger.info("职位页面加载完成")
            except TimeoutException:
                logger.warning("职位页面加载超时")
                return False
            
            # 查找并点击Easy Apply按钮 - 使用多种选择器策略
            easy_apply_button = None
            easy_apply_selectors = [
                # LinkedIn 2024年最新的Easy Apply按钮选择器
                "//button[contains(@class, 'jobs-apply-button') and contains(., 'Easy Apply')]",
                "//button[contains(@aria-label, 'Easy Apply')]",
                "//button[contains(text(), 'Easy Apply')]",
                "//button[contains(@class, 'artdeco-button--primary') and contains(., 'Easy Apply')]",
                "//button[@data-control-name='jobdetails_topcard_inapply']",
                "//button[contains(@class, 'jobs-s-apply') and contains(., 'Easy Apply')]",
                "//button[contains(@class, 'jobs-apply-button--top-card')]",
                # 2024年新增选择器
                "//button[contains(@class, 'jobs-apply-button') and contains(@class, 'artdeco-button--primary')]",
                "//button[@data-test-id='jobs-apply-button']",
                "//button[contains(@class, 'jobs-apply-button--top-card') and contains(., 'Easy Apply')]",
                "//button[contains(@class, 'jobs-apply-button') and @aria-label]",
                "//button[contains(@class, 'jobs-apply-button') and contains(@class, 'artdeco-button')]",
                # 更精确的选择器
                "//button[normalize-space(text())='Easy Apply']",
                "//button[@aria-label='Easy Apply to this job']",
                "//button[contains(@class, 'jobs-apply-button') and not(contains(@class, 'disabled'))]",
                # 中文版本
                "//button[contains(., '轻松申请')]",
                "//button[contains(., '抢先申请')]",
                # 通用Easy Apply按钮
                "//*[contains(@class, 'easy-apply') or contains(@data-control-name, 'easy_apply')]//button",
                "//button[contains(@class, 'apply-button')]",
                # 备用选择器
                "//button[contains(@data-control-name, 'apply')]",
                "//button[contains(@class, 'artdeco-button--primary') and contains(text(), 'Apply')]"
            ]

            try:
                for selector in easy_apply_selectors:
                    try:
                        logger.info(f"尝试选择器: {selector}")
                        easy_apply_button = self.wait.until(
                            EC.element_to_be_clickable((By.XPATH, selector))
                        )
                        if easy_apply_button:
                            logger.info(f"找到Easy Apply按钮，使用选择器: {selector}")
                            break
                    except TimeoutException:
                        continue

                if not easy_apply_button:
                    # 最后尝试：查找所有包含"Easy Apply"文本的按钮
                    logger.info("尝试查找所有包含Easy Apply文本的按钮...")
                    all_buttons = self.driver.find_elements(By.TAG_NAME, "button")
                    for button in all_buttons:
                        if button.is_displayed() and button.is_enabled():
                            button_text = button.text.strip()
                            if "Easy Apply" in button_text or "轻松申请" in button_text:
                                easy_apply_button = button
                                logger.info(f"通过文本匹配找到Easy Apply按钮: {button_text}")
                                break

                if easy_apply_button:
                    # 记录按钮信息
                    button_text = easy_apply_button.text or easy_apply_button.get_attribute('aria-label') or 'Easy Apply'
                    button_class = easy_apply_button.get_attribute('class')
                    logger.info(f"找到Easy Apply按钮: '{button_text}' (class: {button_class})")

                    # 确保按钮可见和可点击
                    if not easy_apply_button.is_displayed():
                        logger.warning("Easy Apply按钮不可见，尝试滚动到按钮位置")
                        self.driver.execute_script("arguments[0].scrollIntoView({behavior: 'smooth', block: 'center'});", easy_apply_button)
                        time.sleep(2)

                    if not easy_apply_button.is_enabled():
                        logger.error("Easy Apply按钮不可点击")
                        return False

                    # 多种点击方法尝试
                    click_success = False

                    # 方法1: 标准点击
                    try:
                        logger.info("尝试方法1: 标准点击")
                        easy_apply_button.click()
                        click_success = True
                        logger.info("方法1成功: 标准点击")
                    except Exception as e:
                        logger.warning(f"方法1失败: {str(e)}")

                    # 方法2: JavaScript点击
                    if not click_success:
                        try:
                            logger.info("尝试方法2: JavaScript点击")
                            self.driver.execute_script("arguments[0].click();", easy_apply_button)
                            click_success = True
                            logger.info("方法2成功: JavaScript点击")
                        except Exception as e:
                            logger.warning(f"方法2失败: {str(e)}")

                    # 方法3: ActionChains点击
                    if not click_success:
                        try:
                            from selenium.webdriver.common.action_chains import ActionChains
                            logger.info("尝试方法3: ActionChains点击")
                            actions = ActionChains(self.driver)
                            actions.move_to_element(easy_apply_button).click().perform()
                            click_success = True
                            logger.info("方法3成功: ActionChains点击")
                        except Exception as e:
                            logger.warning(f"方法3失败: {str(e)}")

                    # 方法4: 强制JavaScript点击
                    if not click_success:
                        try:
                            logger.info("尝试方法4: 强制JavaScript点击")
                            self.driver.execute_script("""
                                arguments[0].style.display = 'block';
                                arguments[0].style.visibility = 'visible';
                                arguments[0].disabled = false;
                                arguments[0].click();
                            """, easy_apply_button)
                            click_success = True
                            logger.info("方法4成功: 强制JavaScript点击")
                        except Exception as e:
                            logger.warning(f"方法4失败: {str(e)}")

                    if not click_success:
                        logger.error("所有点击方法都失败了")
                        return False

                    logger.info("Easy Apply按钮点击成功！")
                    time.sleep(3)  # 等待页面响应

                    # 检查页面是否发生变化
                    current_url = self.driver.current_url
                    logger.info(f"点击后当前URL: {current_url}")

                    # 等待申请弹窗或新页面加载
                    try:
                        # 等待申请相关元素出现
                        WebDriverWait(self.driver, 10).until(
                            EC.any_of(
                                EC.presence_of_element_located((By.CSS_SELECTOR, "[data-test-modal]")),
                                EC.presence_of_element_located((By.CSS_SELECTOR, ".jobs-easy-apply-modal")),
                                EC.presence_of_element_located((By.CSS_SELECTOR, ".artdeco-modal")),
                                EC.presence_of_element_located((By.XPATH, "//button[contains(text(), 'Next') or contains(text(), 'Submit') or contains(text(), 'Review')]"))
                            )
                        )
                        logger.info("申请弹窗或流程已加载")
                    except TimeoutException:
                        logger.warning("未检测到申请弹窗，可能页面加载较慢")
                        # 截图用于调试
                        try:
                            self.driver.save_screenshot(f"log/after_easy_apply_click_{int(time.time())}.png")
                            logger.info("已保存点击后的页面截图")
                        except Exception as e:
                            logger.warning(f"保存截图失败: {str(e)}")
                else:
                    logger.warning(f"未找到Easy Apply按钮: {job_info['title']}")
                    # 截图用于调试
                    try:
                        self.driver.save_screenshot(f"log/no_easy_apply_{int(time.time())}.png")
                        logger.info("已保存调试截图")
                    except Exception as e:
                        logger.warning(f"保存截图失败: {str(e)}")
                    return False

            except Exception as e:
                logger.error(f"点击Easy Apply按钮失败: {str(e)}")
                return False
            
            # 处理申请流程
            success = self._handle_application_process()
            
            if success:
                self.applied_jobs.add(job_id)
                logger.info(f"成功申请职位: {job_info['title']}")
                
                # 随机延迟
                delay = random.randint(*self.config['linkedin']['delay_between_applications'])
                logger.info(f"等待 {delay} 秒后继续...")
                time.sleep(delay)
                
            return success
            
        except Exception as e:
            logger.error(f"申请职位失败 {job_info['title']}: {str(e)}")
            return False
    
    def _handle_application_process(self) -> bool:
        """处理申请流程"""
        try:
            max_steps = 10  # 增加最大步骤数
            current_step = 0

            logger.info("开始处理LinkedIn申请流程...")

            # 记录当前页面状态
            logger.info(f"申请流程开始时URL: {self.driver.current_url}")
            logger.info(f"申请流程开始时页面标题: {self.driver.title}")

            while current_step < max_steps:
                logger.info(f"申请流程步骤 {current_step + 1}/{max_steps}")
                time.sleep(3)  # 增加等待时间

                # 记录当前页面状态
                logger.debug(f"步骤 {current_step + 1} - 当前URL: {self.driver.current_url}")

                # 检查是否有模态框或弹窗
                modals = self.driver.find_elements(By.CSS_SELECTOR, ".artdeco-modal, .jobs-easy-apply-modal, [data-test-modal]")
                if modals:
                    logger.info(f"检测到 {len(modals)} 个模态框")
                else:
                    logger.debug("未检测到模态框")
                
                # 检查是否有问题需要回答
                if self._answer_application_questions():
                    logger.info("回答了申请问题，继续下一步")
                    current_step += 1
                    continue
                
                # 使用智能按钮检测
                button, button_type = self._find_application_button()

                if button and button_type == 'submit':
                    logger.info("找到提交按钮，正在提交申请...")
                    button.click()
                    time.sleep(5)  # 增加等待时间

                    # 检查是否申请成功
                    try:
                        success_message = self.driver.find_element(
                            By.XPATH,
                            "//*[contains(text(), 'Application sent') or contains(text(), '申请已发送') or contains(text(), 'Your application was sent') or contains(text(), 'Application submitted')]"
                        )
                        logger.info("申请成功提交")
                        return True
                    except NoSuchElementException:
                        logger.info("未找到成功消息，但申请可能已提交")
                        pass

                    return True

                elif button and button_type in ['next', 'review']:
                    button_text = button.text or button.get_attribute('aria-label') or f'{button_type} button'
                    logger.info(f"点击{button_type}按钮: {button_text}")

                    # 滚动到按钮位置
                    self.driver.execute_script("arguments[0].scrollIntoView(true);", button)
                    time.sleep(1)

                    button.click()
                    current_step += 1
                    time.sleep(4)  # 增加等待时间

                else:
                    # 检查是否有错误或需要手动处理
                    logger.warning(f"申请流程步骤 {current_step + 1}: 未找到可点击的按钮")

                    # 尝试查找其他可能的按钮
                    all_buttons = self.driver.find_elements(By.TAG_NAME, "button")
                    visible_buttons = [btn for btn in all_buttons if btn.is_displayed() and btn.is_enabled()]

                    if visible_buttons:
                        logger.info(f"找到 {len(visible_buttons)} 个可见按钮:")
                        for i, btn in enumerate(visible_buttons[:5]):  # 显示前5个
                            btn_text = btn.text.strip() or btn.get_attribute('aria-label') or f'Button {i+1}'
                            btn_class = btn.get_attribute('class') or 'no-class'
                            logger.info(f"  按钮 {i+1}: '{btn_text}' (class: {btn_class[:50]})")
                    else:
                        logger.warning("页面上没有找到任何可见的按钮")

                    # 保存当前页面截图用于调试
                    try:
                        self.driver.save_screenshot(f"log/application_stuck_step_{current_step + 1}_{int(time.time())}.png")
                        logger.info(f"已保存步骤 {current_step + 1} 的调试截图")
                    except Exception as e:
                        logger.warning(f"保存截图失败: {str(e)}")

                    break
            
            logger.warning("申请流程未能完成，可能需要手动处理")
            return False

        except Exception as e:
            logger.error(f"处理申请流程失败: {str(e)}")
            return False

    def _find_application_button(self) -> tuple:
        """智能查找申请流程中的按钮

        Returns:
            (button_element, button_type) - button_type可以是'next', 'submit', 'review'
        """
        try:
            # 定义按钮选择器和类型
            button_selectors = [
                # 提交按钮 - 优先级最高
                ("//button[@aria-label='Submit application']", 'submit'),
                ("//button[contains(@class, 'artdeco-button--primary') and contains(., 'Submit application')]", 'submit'),
                ("//button[contains(@class, 'artdeco-button--primary') and contains(., 'Submit')]", 'submit'),
                ("//button[contains(text(), 'Submit application')]", 'submit'),
                ("//button[contains(text(), 'Submit')]", 'submit'),

                # Review按钮
                ("//button[contains(@class, 'artdeco-button--primary') and contains(., 'Review')]", 'review'),
                ("//button[contains(text(), 'Review')]", 'review'),
                ("//button[contains(text(), '审核')]", 'review'),

                # Next按钮
                ("//button[@aria-label='Continue to next step']", 'next'),
                ("//button[contains(@class, 'artdeco-button--primary') and contains(., 'Next')]", 'next'),
                ("//button[contains(text(), 'Next')]", 'next'),
                ("//button[contains(text(), '下一步')]", 'next'),
            ]

            for selector, button_type in button_selectors:
                try:
                    button = self.driver.find_element(By.XPATH, selector)
                    if button.is_displayed() and button.is_enabled():
                        logger.info(f"找到{button_type}按钮: {button.text or button.get_attribute('aria-label')}")
                        return button, button_type
                except NoSuchElementException:
                    continue

            return None, None

        except Exception as e:
            logger.warning(f"查找申请按钮失败: {str(e)}")
            return None, None
    
    def _answer_application_questions(self) -> bool:
        """回答申请问题"""
        try:
            answered = False
            
            # 查找文本输入框
            text_inputs = self.driver.find_elements(
                By.CSS_SELECTOR, 
                "input[type='text'], input[type='number'], textarea"
            )
            
            for input_field in text_inputs:
                if input_field.is_displayed() and input_field.is_enabled():
                    placeholder = input_field.get_attribute('placeholder') or ''
                    label = self._get_field_label(input_field)
                    
                    answer = self._get_answer_for_question(placeholder + ' ' + label)
                    if answer and not input_field.get_attribute('value'):
                        input_field.clear()
                        self._human_type(input_field, answer)
                        answered = True
            
            # 查找单选按钮
            radio_groups = self.driver.find_elements(
                By.CSS_SELECTOR,
                "fieldset, .fb-radio-buttons"
            )
            
            for group in radio_groups:
                if group.is_displayed():
                    radios = group.find_elements(By.CSS_SELECTOR, "input[type='radio']")
                    if radios and not any(radio.is_selected() for radio in radios):
                        # 选择第一个选项（通常是"是"或积极的回答）
                        radios[0].click()
                        answered = True
            
            # 查找下拉选择框
            selects = self.driver.find_elements(By.CSS_SELECTOR, "select")
            for select in selects:
                if select.is_displayed() and select.is_enabled():
                    options = select.find_elements(By.CSS_SELECTOR, "option")
                    if len(options) > 1 and not select.get_attribute('value'):
                        # 选择第二个选项（跳过默认的空选项）
                        options[1].click()
                        answered = True
            
            return answered
            
        except Exception as e:
            logger.warning(f"回答申请问题失败: {str(e)}")
            return False
    
    def _get_field_label(self, input_field) -> str:
        """获取输入框的标签"""
        try:
            # 尝试多种方式获取标签
            label_id = input_field.get_attribute('aria-labelledby')
            if label_id:
                label_element = self.driver.find_element(By.ID, label_id)
                return label_element.text
            
            # 查找相邻的label元素
            parent = input_field.find_element(By.XPATH, '..')
            label = parent.find_element(By.CSS_SELECTOR, 'label')
            return label.text
            
        except:
            return ''
    
    def _get_answer_for_question(self, question_text: str) -> str:
        """根据问题文本获取答案"""
        question_lower = question_text.lower()
        default_answers = self.config['linkedin']['default_answers']
        
        if 'experience' in question_lower or '经验' in question_lower:
            return default_answers.get('years_experience', '3')
        elif 'relocate' in question_lower or '搬迁' in question_lower:
            return default_answers.get('willing_to_relocate', 'Yes')
        elif 'authorized' in question_lower or 'work authorization' in question_lower:
            return default_answers.get('authorized_to_work', 'Yes')
        elif 'sponsorship' in question_lower or '赞助' in question_lower:
            return default_answers.get('require_sponsorship', 'No')
        elif 'phone' in question_lower or '电话' in question_lower:
            return '1234567890'  # 示例电话号码
        
        return ''
    
    def _human_type(self, element, text: str):
        """模拟人类打字"""
        for char in text:
            element.send_keys(char)
            time.sleep(random.uniform(0.05, 0.15))
    
    def batch_apply_jobs(self, max_applications: int = None) -> Dict:
        """批量申请职位"""
        if not max_applications:
            max_applications = self.config['linkedin']['max_applications_per_day']
        
        results = {
            'total_found': 0,
            'total_applied': 0,
            'successful_applications': [],
            'failed_applications': []
        }
        
        try:
            # 搜索职位
            for keyword in self.config['linkedin']['search_keywords']:
                if results['total_applied'] >= max_applications:
                    break
                    
                jobs = self.search_jobs(keyword)
                results['total_found'] += len(jobs)
                
                for job in jobs:
                    if results['total_applied'] >= max_applications:
                        break
                    
                    if self.apply_to_job(job):
                        results['total_applied'] += 1
                        results['successful_applications'].append(job)
                    else:
                        results['failed_applications'].append(job)
            
            logger.info(f"批量申请完成: 找到 {results['total_found']} 个职位，成功申请 {results['total_applied']} 个")
            return results
            
        except Exception as e:
            logger.error(f"批量申请失败: {str(e)}")
            return results
    
    def close(self):
        """关闭浏览器"""
        if self.driver:
            self.driver.quit()
            logger.info("浏览器已关闭")
    
    def __enter__(self):
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.close()


# 使用示例
if __name__ == "__main__":
    # 创建配置文件示例
    config = {
        'linkedin': {
            'email': '<EMAIL>',
            'password': 'your_password',
            'search_keywords': ['python developer', 'software engineer', 'data scientist'],
            'location': 'United States',
            'max_applications_per_day': 20,
            'delay_between_applications': [30, 60],
            'auto_answer_questions': True,
            'default_answers': {
                'years_experience': '3',
                'willing_to_relocate': 'Yes',
                'authorized_to_work': 'Yes',
                'require_sponsorship': 'No'
            }
        },
        'selenium': {
            'headless': False,
            'implicit_wait': 10,
            'page_load_timeout': 30
        }
    }
    
    # 保存配置文件
    with open('linkedin_config.yaml', 'w', encoding='utf-8') as f:
        yaml.dump(config, f, default_flow_style=False, allow_unicode=True)
    
    # 使用自动化工具
    with LinkedInAutomation('linkedin_config.yaml') as linkedin:
        linkedin.setup_driver()
        
        if linkedin.login():
            results = linkedin.batch_apply_jobs(max_applications=10)
            print(f"申请结果: {results}")