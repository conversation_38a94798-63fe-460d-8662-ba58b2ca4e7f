from fastapi import <PERSON><PERSON><PERSON>, HTTPException, Depends, UploadFile, File
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import Optional
import yaml
import os
from pathlib import Path
import tempfile
import PyPDF2
from io import BytesIO
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from loguru import logger
import sys

# Add the project root to the Python path for absolute imports from src
project_root = Path(__file__).resolve().parents[2] # d:\Jobs_Applier_AI_Agent_AIHawk-main
if str(project_root) not in sys.path:
    sys.path.insert(0, str(project_root))

DATA_FOLDER = project_root / "data_folder"

# Now imports from 'src.' should work as 'src' is a top-level package in the project_root
from src.libs.resume_and_cover_builder.resume_facade import ResumeFacade
from src.libs.resume_and_cover_builder.style_manager import StyleManager
from src.libs.resume_and_cover_builder.resume_generator import ResumeGenerator
from src.resume_schemas.resume import Resume
from src.utils.chrome_utils import init_browser # Added import
import sys
import os
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../../..')))
import config

global_config = None
try:
    from src.libs.resume_and_cover_builder.config import global_config
except ImportError:
    try:
        from src.libs.resume_and_cover_builder import config as config_module
        global_config = getattr(config_module, 'global_config', None)
    except Exception:
        pass


# Import LinkedIn automation API
from webui.backend.linkedin_api import router as linkedin_router
# Import Settings API
from webui.backend.settings_api import router as settings_router

app = FastAPI(title="AIHawk Job Application Assistant", version="1.0.0")

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # In production, replace with specific origins
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include LinkedIn automation router
app.include_router(linkedin_router)
# Include Settings router
app.include_router(settings_router)

# 从secrets.yaml获取API密钥
def load_api_key():
    try:
        secrets_path = DATA_FOLDER / "secrets.yaml"
        with open(secrets_path, 'r', encoding='utf-8') as file:
            secrets = yaml.safe_load(file)
            api_key = secrets.get('llm_api_key', '')
            if not api_key:
                print("警告: API密钥未在secrets.yaml中找到")
                return ''
            print("成功: API密钥已从secrets.yaml加载")
            return api_key
    except FileNotFoundError:
        print(f"错误: 未找到secrets.yaml文件，请确保文件存在于{str(DATA_FOLDER)}目录中")
        return ''
    except Exception as e:
        print(f"错误: 加载API密钥时发生异常: {e}")
        return ''

# 加载简历对象
def load_resume_object():
    try:
        resume_path = DATA_FOLDER / "plain_text_resume.yaml"
        with open(resume_path, 'r', encoding='utf-8') as file:
            yaml_content = file.read()
            return Resume(yaml_content)
    except Exception as e:
        print(f"Error loading resume object: {e}")
        return None

# 初始化核心组件
api_key = load_api_key()
style_manager = StyleManager()
resume_generator = ResumeGenerator()
resume_object = load_resume_object()

resume_facade = ResumeFacade(
    api_key=api_key,
    style_manager=style_manager,
    resume_generator=resume_generator,
    resume_object=resume_object,
    output_path=str(DATA_FOLDER / "output")
)

# 设置 Selenium driver
# chrome_options = webdriver.ChromeOptions()
# chrome_options.add_argument('--headless')
# driver = webdriver.Chrome(executable_path="C:/Program Files/chromedriver-win64/chromedriver.exe", options=chrome_options)
# resume_facade.set_driver(driver)

try:
    logger.info("Initializing WebDriver...")
    driver = init_browser(headless=True)  # Use init_browser from chrome_utils
    if driver:
        resume_facade.set_driver(driver)
        logger.info("WebDriver initialized and set successfully.")
    else:
        logger.error("Failed to initialize WebDriver. Driver is None.")
        # Decide how to handle this: exit, or run without Selenium features?
        # For now, we'll log the error and continue, some features might not work.
except Exception as e:
    logger.error(f"Error initializing WebDriver: {e}", exc_info=True)
    # Decide how to handle this, for now, log and continue.


@app.get("/api/health")
def health_check():
    return {"status": "ok"}

def generate_cover_letter_content(job_info, optimized_resume_html):
    """
    生成求职信HTML内容
    """
    try:
        api_key = global_config.API_KEY
        if not api_key:
            raise Exception("API密钥未配置")

        from src.libs.resume_and_cover_builder.llm.llm_job_parser import LLMParser
        llm_parser = LLMParser(api_key=api_key)

        # 从简历HTML中提取关键信息
        resume_text = extract_text_from_html(optimized_resume_html)

        # 从简历HTML中提取用户姓名
        user_name = extract_user_name_from_html(optimized_resume_html)

        # 生成求职信内容
        cover_letter_content = llm_parser.generate_cover_letter(job_info, resume_text, user_name)

        # 生成完整的HTML，传入用户姓名
        cover_letter_html = create_cover_letter_html(job_info, cover_letter_content, user_name)

        return cover_letter_html

    except Exception as e:
        print(f"求职信生成错误: {str(e)}")
        raise e

def extract_text_from_html(html_content):
    """从HTML中提取纯文本"""
    try:
        from bs4 import BeautifulSoup
        soup = BeautifulSoup(html_content, 'html.parser')
        return soup.get_text(strip=True)
    except:
        # 如果BeautifulSoup不可用，使用简单的正则表达式
        import re
        text = re.sub(r'<[^>]+>', '', html_content)
        return re.sub(r'\s+', ' ', text).strip()

def extract_user_name_from_html(html_content):
    """从简历HTML中提取用户姓名"""
    try:
        from bs4 import BeautifulSoup
        soup = BeautifulSoup(html_content, 'html.parser')

        # 尝试从.name类中提取姓名
        name_element = soup.find(class_='name')
        if name_element:
            return name_element.get_text().strip()

        # 尝试从h1标签中提取姓名
        h1_element = soup.find('h1')
        if h1_element:
            return h1_element.get_text().strip()

        # 尝试从header中的第一个文本节点提取
        header = soup.find('header')
        if header:
            # 查找header中的第一个非空文本
            for element in header.find_all(['h1', 'h2', 'h3', 'div', 'p']):
                text = element.get_text().strip()
                if text and len(text) < 50:  # 姓名通常不会太长
                    return text

        return "此致敬礼"  # 默认值
    except Exception as e:
        print(f"姓名提取错误: {str(e)}")
        return "此致敬礼"

def extract_company_logo_from_url(job_url):
    """从职位URL页面抓取公司Logo - 使用Playwright实现"""
    try:
        import asyncio
        from playwright.async_api import async_playwright
        import base64

        async def get_logo_async():
            async with async_playwright() as p:
                browser = await p.chromium.launch(headless=True)
                page = await browser.new_page()

                try:
                    print(f"正在访问职位页面: {job_url}")
                    # 设置更短的超时时间，不等待网络空闲
                    await page.goto(job_url, timeout=15000, wait_until='domcontentloaded')
                    # 等待页面基本加载完成
                    await page.wait_for_timeout(3000)

                    # LinkedIn特定的Logo选择器（优先级从高到低）
                    linkedin_logo_selectors = [
                        # LinkedIn公司页面Logo
                        '.org-top-card-summary__logo img',
                        '.org-top-card-summary-info-list__logo img',
                        '.company-logo img',
                        '.org-top-card__logo img',

                        # LinkedIn职位页面公司Logo
                        '.job-details-jobs-unified-top-card__company-name img',
                        '.jobs-unified-top-card__company-name img',
                        '.job-details-jobs-unified-top-card__content img',
                        '.jobs-unified-top-card__content img',
                        '.jobs-company__box img',
                        '.jobs-company img',

                        # 通用LinkedIn图片选择器
                        'img[alt*="logo" i]',
                        'img[src*="logo" i]',
                        'img[src*="company" i]',
                        'img[alt*="company" i]',

                        # 其他可能的选择器
                        '.company-header img',
                        '.employer-logo img',
                        'img[class*="company" i]',
                        'img[class*="employer" i]',
                        '.job-header img',
                        'header img'
                    ]

                    logo_url = None

                    print(f"开始搜索Logo，共{len(linkedin_logo_selectors)}个选择器")

                    for i, selector in enumerate(linkedin_logo_selectors):
                        try:
                            print(f"尝试选择器 {i+1}: {selector}")
                            logo_elements = await page.query_selector_all(selector)
                            print(f"找到 {len(logo_elements)} 个匹配元素")

                            for j, logo_element in enumerate(logo_elements):
                                try:
                                    src = await logo_element.get_attribute('src')
                                    alt = await logo_element.get_attribute('alt') or ''

                                    print(f"  元素 {j+1}: src={src}, alt={alt}")

                                    # 检查是否是有效的Logo
                                    if src and (
                                        'logo' in src.lower() or
                                        'company' in src.lower() or
                                        'logo' in alt.lower() or
                                        'company' in alt.lower()
                                    ):
                                        # 尝试截取Logo图片并转换为base64
                                        try:
                                            # 滚动到元素位置
                                            await logo_element.scroll_into_view_if_needed()
                                            await page.wait_for_timeout(1000)

                                            # 截取Logo元素
                                            logo_screenshot = await logo_element.screenshot()
                                            logo_base64 = base64.b64encode(logo_screenshot).decode('utf-8')
                                            logo_url = f"data:image/png;base64,{logo_base64}"

                                            print(f"成功截取Logo: 选择器={selector}, base64长度={len(logo_base64)}")
                                            break

                                        except Exception as screenshot_error:
                                            print(f"截图失败: {screenshot_error}")
                                            # 如果截图失败，尝试使用原始URL
                                            if src and src.startswith('http'):
                                                logo_url = src
                                                print(f"使用原始URL: {src}")
                                                break

                                except Exception as element_error:
                                    print(f"处理元素失败: {element_error}")
                                    continue

                            if logo_url:
                                print(f"找到Logo，停止搜索")
                                break

                        except Exception as selector_error:
                            print(f"选择器 {selector} 失败: {selector_error}")
                            continue

                    await browser.close()
                    return logo_url

                except Exception as e:
                    print(f"Logo抓取失败: {e}")
                    await browser.close()
                    return None

        # 运行异步函数
        try:
            loop = asyncio.get_event_loop()
        except RuntimeError:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

        logo_result = loop.run_until_complete(get_logo_async())

        if logo_result:
            print(f"Logo抓取成功: {logo_result[:100]}...")
            return logo_result
        else:
            print("未找到合适的Logo")
            return None

    except Exception as e:
        print(f"Logo抓取初始化失败: {e}")
        return None

def create_cover_letter_html(job_info, cover_letter_content, user_name="此致敬礼"):
    """创建现代美观的求职信HTML模板"""
    company = job_info.get('company', '目标公司')
    role = job_info.get('role', '目标职位')
    location = job_info.get('location', '工作地点')
    job_url = job_info.get('url', '')

    # 调试日志：验证职位URL传递
    print(f"求职信生成 - 职位信息: 公司={company}, 职位={role}, 地点={location}")
    print(f"求职信生成 - 职位URL: {job_url}")
    print(f"求职信生成 - 完整job_info: {job_info}")

    # 尝试从职位页面抓取Logo
    extracted_logo = None
    if job_url:
        extracted_logo = extract_company_logo_from_url(job_url)

    # 获取公司Logo URL，优先使用抓取的Logo
    company_clean = company.lower().replace(' ', '').replace('&', '').replace(',', '').replace('.', '')
    if extracted_logo:
        logo_urls = [extracted_logo, f"https://logo.clearbit.com/{company_clean}.com"]
    else:
        logo_urls = [
            f"https://logo.clearbit.com/{company_clean}.com",
            f"https://img.logo.dev/{company_clean}.com?token=pk_X-1ZO13GSgeOoUrIuJ6GMQ",
            f"https://logo.clearbit.com/{company.lower().replace(' ', '')}.com"
        ]

    # 生成当前日期
    from datetime import datetime
    current_date = datetime.now().strftime("%Y年%m月%d日")

    html_template = f"""
    <!DOCTYPE html>
    <html lang="zh-CN">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>求职信 - {role} @ {company}</title>
        <style>
            @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=Playfair+Display:wght@400;500;600;700&family=Kalam:wght@400;700&family=Dancing+Script:wght@400;500;600;700&display=swap');

            * {{
                margin: 0;
                padding: 0;
                box-sizing: border-box;
            }}

            body {{
                font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
                line-height: 1.7;
                color: #1a202c;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
                min-height: 100vh;
                padding: 30px 20px;
                position: relative;
            }}

            body::before {{
                content: '';
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="dots" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23dots)"/></svg>');
                z-index: -1;
            }}

            .container {{
                max-width: 900px;
                margin: 0 auto;
                background: rgba(255, 255, 255, 0.98);
                backdrop-filter: blur(20px);
                border-radius: 24px;
                box-shadow:
                    0 32px 64px rgba(0, 0, 0, 0.12),
                    0 0 0 1px rgba(255, 255, 255, 0.05);
                overflow: hidden;
                position: relative;
            }}

            .container::before {{
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                height: 6px;
                background: linear-gradient(90deg, #667eea, #764ba2, #f093fb, #667eea);
                background-size: 300% 100%;
                animation: gradientShift 8s ease-in-out infinite;
            }}

            @keyframes gradientShift {{
                0%, 100% {{ background-position: 0% 50%; }}
                50% {{ background-position: 100% 50%; }}
            }}

            .header {{
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                padding: 45px 40px;
                text-align: center;
                position: relative;
                overflow: hidden;
                min-height: 180px;
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: center;
            }}

            .header::before {{
                content: '';
                position: absolute;
                top: -50%;
                left: -50%;
                width: 200%;
                height: 200%;
                background: radial-gradient(circle, rgba(255,255,255,0.1) 1px, transparent 1px);
                background-size: 30px 30px;
                animation: float 20s linear infinite;
            }}

            @keyframes float {{
                0% {{ transform: translate(-50%, -50%) rotate(0deg); }}
                100% {{ transform: translate(-50%, -50%) rotate(360deg); }}
            }}

            .company-logo {{
                width: 70px;
                height: 70px;
                border-radius: 16px;
                margin: 0 auto 20px;
                display: flex;
                align-items: center;
                justify-content: center;
                position: relative;
                z-index: 2;
                transition: transform 0.3s ease;
                background: rgba(255, 255, 255, 0.15);
                backdrop-filter: blur(10px);
                border: 1px solid rgba(255, 255, 255, 0.2);
                box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            }}

            .company-logo:hover {{
                transform: translateY(-3px) scale(1.03);
                background: rgba(255, 255, 255, 0.2);
            }}

            .company-logo img {{
                width: 45px;
                height: 45px;
                object-fit: contain;
                border-radius: 6px;
                filter: brightness(1.1) contrast(1.1);
            }}

            .header-title {{
                font-family: 'Inter', 'Microsoft YaHei', serif;
                font-size: 32px;
                font-weight: 700;
                margin-bottom: 15px;
                position: relative;
                z-index: 2;
                text-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                color: #ffffff;
                letter-spacing: 0.5px;
                line-height: 1.2;
            }}

            .header-subtitle {{
                font-size: 16px;
                opacity: 0.98;
                position: relative;
                z-index: 2;
                font-weight: 500;
                letter-spacing: 0.3px;
                color: rgba(255, 255, 255, 0.95);
                line-height: 1.4;
                font-family: 'Inter', 'Microsoft YaHei', sans-serif;
            }}

            .header-subtitle a {{
                color: #ffffff !important;
                text-decoration: none;
                font-weight: 600;
                border-bottom: 1px solid rgba(255, 255, 255, 0.3);
                padding-bottom: 1px;
                transition: all 0.3s ease;
            }}

            .header-subtitle a:hover {{
                border-bottom-color: rgba(255, 255, 255, 0.8);
                text-shadow: 0 0 8px rgba(255, 255, 255, 0.3);
            }}

            .header-subtitle .separator {{
                margin: 0 8px;
                opacity: 0.7;
                font-weight: 300;
            }}

            .content {{
                padding: 60px 50px;
                background: white;
            }}

            .greeting {{
                font-size: 20px;
                font-weight: 600;
                margin-bottom: 35px;
                color: #2d3748;
                position: relative;
                padding-left: 0px;
                text-align: left !important;
                display: block;
                width: 100%;
            }}

            .greeting::before {{
                content: '';
                position: absolute;
                left: 0;
                top: 0;
                font-size: 18px;
            }}

            .main-content {{
                font-size: 17px;
                line-height: 1.8;
                margin-bottom: 45px;
                color: #4a5568;
                text-align: justify;
            }}

            .chinese-content {{
                margin-bottom: 30px;
                font-family: 'Inter', 'Microsoft YaHei', sans-serif;
                text-align: left;
            }}

            .english-content {{
                margin-top: 30px;
                font-family: 'Inter', sans-serif;
                font-style: italic;
                text-align: left;
            }}

            .separator {{
                margin: 30px 0;
                text-align: center;
                width: 100%;
            }}

            .separator hr {{
                border: none;
                height: 2px;
                background: linear-gradient(90deg, #667eea, #764ba2);
                margin: 0 auto;
                width: 100%;
                border-radius: 1px;
            }}

            .highlight {{
                background: linear-gradient(120deg, #a8edea 0%, #fed6e3 100%);
                padding: 3px 8px;
                border-radius: 6px;
                font-weight: 600;
                color: #2d3748;
                box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
            }}

            .stats-section {{
                background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
                border-radius: 20px;
                padding: 25px;
                margin: 25px 0;
                border: 1px solid #e2e8f0;
                position: relative;
                overflow: hidden;
            }}

            .stats-section::before {{
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 4px;
                background: linear-gradient(90deg, #667eea, #764ba2);
            }}

            .stats-title {{
                font-size: 22px;
                font-weight: 700;
                margin-bottom: 15px;
                color: #2d3748;
                display: flex;
                align-items: center;
                gap: 10px;
            }}

            .stats-grid {{
                display: grid;
                grid-template-columns: repeat(3, 1fr);
                gap: 15px;
                margin-top: 15px;
            }}

            .stat-card {{
                background: white;
                padding: 15px 12px;
                border-radius: 16px;
                text-align: center;
                box-shadow:
                    0 10px 25px rgba(0, 0, 0, 0.08),
                    0 0 0 1px rgba(0, 0, 0, 0.05);
                border: 1px solid #f1f5f9;
                transition: all 0.3s ease;
                position: relative;
                overflow: hidden;
                min-width: 0;
                flex: 1;
            }}

            .stat-card::before {{
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 3px;
                background: linear-gradient(90deg, #667eea, #764ba2);
                transform: scaleX(0);
                transition: transform 0.3s ease;
            }}

            .stat-card:hover {{
                transform: translateY(-5px);
                box-shadow: 0 20px 40px rgba(0, 0, 0, 0.12);
            }}

            .stat-card:hover::before {{
                transform: scaleX(1);
            }}

            .stat-value {{
                font-size: 32px;
                font-weight: 800;
                background: linear-gradient(135deg, #667eea, #764ba2);
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;
                background-clip: text;
                margin-bottom: 8px;
                line-height: 1.2;
                position: relative;
            }}

            .stat-value::after {{
                content: '';
                position: absolute;
                bottom: -10px;
                left: 50%;
                transform: translateX(-50%);
                width: 40px;
                height: 3px;
                background: linear-gradient(90deg, #667eea, #764ba2);
                border-radius: 2px;
                opacity: 0.6;
            }}

            .stat-label {{
                font-size: 15px;
                color: #718096;
                font-weight: 600;
                letter-spacing: 0.5px;
            }}

            /* 圆形进度条样式 */
            .progress-circle {{
                width: 80px;
                height: 80px;
                margin: 0 auto 15px;
                position: relative;
            }}

            .progress-circle svg {{
                width: 100%;
                height: 100%;
                transform: rotate(-90deg);
            }}

            .progress-circle .progress-bg {{
                fill: none;
                stroke: #e2e8f0;
                stroke-width: 8;
            }}

            .progress-circle .progress-bar {{
                fill: none;
                stroke: url(#gradient);
                stroke-width: 8;
                stroke-linecap: round;
                stroke-dasharray: 251.2;
                stroke-dashoffset: 251.2;
                animation: progressAnimation 2s ease-out forwards;
            }}

            @keyframes progressAnimation {{
                to {{
                    stroke-dashoffset: calc(251.2 - (251.2 * var(--progress)) / 100);
                }}
            }}

            .progress-text {{
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                font-size: 18px;
                font-weight: 700;
                color: #2d3748;
            }}

            /* 技能雷达图样式 */
            .radar-chart {{
                width: 100%;
                height: 200px;
                margin: 20px 0;
                position: relative;
                background: radial-gradient(circle, rgba(102, 126, 234, 0.1) 0%, transparent 70%);
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
            }}

            .radar-chart::before {{
                content: '';
                position: absolute;
                width: 80%;
                height: 80%;
                border: 2px solid rgba(102, 126, 234, 0.2);
                border-radius: 50%;
            }}

            .radar-chart::after {{
                content: '';
                position: absolute;
                width: 60%;
                height: 60%;
                border: 2px solid rgba(102, 126, 234, 0.3);
                border-radius: 50%;
            }}

            .radar-point {{
                position: absolute;
                width: 8px;
                height: 8px;
                background: linear-gradient(135deg, #667eea, #764ba2);
                border-radius: 50%;
                box-shadow: 0 0 10px rgba(102, 126, 234, 0.5);
                animation: pulse 2s infinite;
            }}

            @keyframes pulse {{
                0%, 100% {{
                    transform: scale(1);
                    opacity: 1;
                }}
                50% {{
                    transform: scale(1.2);
                    opacity: 0.8;
                }}
            }}

            /* 匹配度条形图 */
            .match-bar {{
                width: 100%;
                height: 12px;
                background: #e2e8f0;
                border-radius: 6px;
                overflow: hidden;
                margin: 10px 0;
                position: relative;
            }}

            .match-fill {{
                height: 100%;
                background: linear-gradient(90deg, #667eea, #764ba2);
                border-radius: 6px;
                width: 0%;
                animation: fillAnimation 2s ease-out forwards;
                position: relative;
            }}

            .match-fill::after {{
                content: '';
                position: absolute;
                top: 0;
                right: 0;
                width: 20px;
                height: 100%;
                background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3));
                animation: shimmer 2s infinite;
            }}

            @keyframes fillAnimation {{
                to {{
                    width: var(--match-percentage);
                }}
            }}

            @keyframes shimmer {{
                0% {{
                    transform: translateX(-20px);
                }}
                100% {{
                    transform: translateX(20px);
                }}
            }}

            .closing {{
                font-size: 17px;
                margin-top: 45px;
                color: #2d3748;
                line-height: 1.8;
                font-family: 'Inter', 'Microsoft YaHei', sans-serif;
            }}

            .signature {{
                margin-top: 50px;
                text-align: right;
                position: relative;
                font-family: 'Inter', 'Microsoft YaHei', sans-serif;
            }}

            .signature-name {{
                font-family: 'Kalam', 'Dancing Script', cursive;
                font-size: 24px;
                font-weight: 600;
                color: #2d3748;
                margin-bottom: 8px;
                transform: rotate(-2deg);
                display: inline-block;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
            }}

            .signature-date {{
                font-size: 17px;
                color: #2d3748;
                font-weight: 500;
                font-family: 'Inter', 'Microsoft YaHei', sans-serif;
            }}

            .footer {{
                background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
                padding: 30px 50px;
                text-align: center;
                font-size: 15px;
                color: #718096;
                border-top: 1px solid #e2e8f0;
                position: relative;
            }}

            .footer::before {{
                content: '';
                position: absolute;
                top: 0;
                left: 50%;
                transform: translateX(-50%);
                width: 60px;
                height: 3px;
                background: linear-gradient(90deg, #667eea, #764ba2);
                border-radius: 0 0 3px 3px;
            }}

            @media (max-width: 768px) {{
                body {{
                    padding: 20px 15px;
                }}

                .container {{
                    border-radius: 20px;
                }}

                .header {{
                    padding: 40px 30px;
                }}

                .content {{
                    padding: 40px 30px;
                }}

                .footer {{
                    padding: 25px 30px;
                }}

                .stats-section {{
                    padding: 20px;
                    margin: 20px 0;
                }}

                .stats-grid {{
                    grid-template-columns: repeat(3, 1fr);
                    gap: 12px;
                }}

                .stat-card {{
                    padding: 12px 8px;
                }}

                .stat-value {{
                    font-size: 28px;
                }}

                .stat-label {{
                    font-size: 12px;
                }}

                .header-title {{
                    font-size: 28px;
                }}

                .company-logo {{
                    width: 70px;
                    height: 70px;
                }}
            }}

            /* 小屏幕优化 - 保持三列布局 */
            @media (max-width: 600px) {{
                .stats-section {{
                    padding: 15px;
                    margin: 15px 0;
                }}

                .stats-grid {{
                    grid-template-columns: repeat(3, 1fr);
                    gap: 8px;
                }}

                .stat-card {{
                    padding: 10px 6px;
                }}

                .stat-value {{
                    font-size: 24px;
                }}

                .stat-label {{
                    font-size: 11px;
                }}
            }}

            /* PDF打印优化样式 */
            @media print {{
                @page {{
                    size: A4;
                    margin: 1.2in 0.8in;
                    -webkit-print-color-adjust: exact;
                    print-color-adjust: exact;
                    color-adjust: exact;
                }}

                /* 强制显示背景色和边框 */
                * {{
                    -webkit-print-color-adjust: exact !important;
                    print-color-adjust: exact !important;
                    color-adjust: exact !important;
                }}

                body {{
                    background: white !important;
                    padding: 0 !important;
                    margin: 0 !important;
                    font-size: 12px !important;
                    line-height: 1.4 !important;
                    -webkit-font-smoothing: antialiased !important;
                    font-smoothing: antialiased !important;
                }}

                .container {{
                    box-shadow: none !important;
                    border-radius: 0 !important;
                    margin: 0 !important;
                    padding: 0 !important;
                    background: white !important;
                }}

                .header {{
                    padding: 15px 0 !important;
                    margin-bottom: 10px !important;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
                    -webkit-print-color-adjust: exact !important;
                    print-color-adjust: exact !important;
                    color-adjust: exact !important;
                }}

                .content {{
                    padding: 10px 0 !important;
                    margin: 0 !important;
                }}

                .stats-section {{
                    padding: 12px !important;
                    margin: 15px 0 !important;
                    background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%) !important;
                    border: 2px solid #e2e8f0 !important;
                    border-radius: 12px !important;
                    page-break-inside: avoid;
                    break-inside: avoid;
                    -webkit-print-color-adjust: exact !important;
                    print-color-adjust: exact !important;
                    color-adjust: exact !important;
                    position: relative !important;
                }}

                .stats-section::before {{
                    content: '' !important;
                    position: absolute !important;
                    top: 0 !important;
                    left: 0 !important;
                    width: 100% !important;
                    height: 4px !important;
                    background: linear-gradient(90deg, #667eea, #764ba2) !important;
                    -webkit-print-color-adjust: exact !important;
                    print-color-adjust: exact !important;
                    color-adjust: exact !important;
                }}

                .stats-title {{
                    font-size: 16px !important;
                    margin-bottom: 8px !important;
                    line-height: 1.3 !important;
                    color: #2d3748 !important;
                    font-weight: 700 !important;
                }}

                .stats-grid {{
                    gap: 8px !important;
                    margin-top: 8px !important;
                    display: grid !important;
                    grid-template-columns: repeat(3, 1fr) !important;
                }}

                .stat-card {{
                    padding: 8px 6px !important;
                    background: white !important;
                    border: 2px solid #f1f5f9 !important;
                    border-radius: 8px !important;
                    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1) !important;
                    text-align: center !important;
                    -webkit-print-color-adjust: exact !important;
                    print-color-adjust: exact !important;
                    color-adjust: exact !important;
                    display: block !important;
                    position: relative !important;
                }}

                .stat-card::before {{
                    content: '' !important;
                    position: absolute !important;
                    top: 0 !important;
                    left: 0 !important;
                    width: 100% !important;
                    height: 3px !important;
                    background: linear-gradient(90deg, #667eea, #764ba2) !important;
                    -webkit-print-color-adjust: exact !important;
                    print-color-adjust: exact !important;
                    color-adjust: exact !important;
                }}

                .stat-card::after {{
                    display: none !important;
                }}

                .stat-value {{
                    font-size: 20px !important;
                    margin-bottom: 4px !important;
                    color: #2d3748 !important;
                    background: linear-gradient(135deg, #667eea, #764ba2) !important;
                    -webkit-background-clip: text !important;
                    -webkit-text-fill-color: transparent !important;
                    background-clip: text !important;
                    line-height: 1.2 !important;
                    font-weight: 800 !important;
                    -webkit-print-color-adjust: exact !important;
                    print-color-adjust: exact !important;
                    color-adjust: exact !important;
                }}

                .stat-value::after {{
                    content: '' !important;
                    position: absolute !important;
                    bottom: -6px !important;
                    left: 50% !important;
                    transform: translateX(-50%) !important;
                    width: 30px !important;
                    height: 2px !important;
                    background: linear-gradient(90deg, #667eea, #764ba2) !important;
                    border-radius: 1px !important;
                    -webkit-print-color-adjust: exact !important;
                    print-color-adjust: exact !important;
                    color-adjust: exact !important;
                }}

                .stat-label {{
                    font-size: 11px !important;
                    color: #718096 !important;
                    line-height: 1.2 !important;
                    margin-top: 4px !important;
                    font-weight: 600 !important;
                    letter-spacing: 0.3px !important;
                }}

                .footer {{
                    padding: 10px 0 !important;
                    margin-top: 15px !important;
                    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%) !important;
                    border-top: 2px solid #e2e8f0 !important;
                    text-align: center !important;
                    font-size: 10px !important;
                    color: #718096 !important;
                    -webkit-print-color-adjust: exact !important;
                    print-color-adjust: exact !important;
                    color-adjust: exact !important;
                }}

                .footer::before {{
                    content: '' !important;
                    position: absolute !important;
                    top: 0 !important;
                    left: 50% !important;
                    transform: translateX(-50%) !important;
                    width: 40px !important;
                    height: 2px !important;
                    background: linear-gradient(90deg, #667eea, #764ba2) !important;
                    border-radius: 0 0 2px 2px !important;
                    -webkit-print-color-adjust: exact !important;
                    print-color-adjust: exact !important;
                    color-adjust: exact !important;
                }}

                .signature {{
                    margin-top: 20px !important;
                    text-align: right !important;
                    position: relative !important;
                }}

                .signature-name {{
                    font-size: 16px !important;
                    font-weight: 600 !important;
                    color: #2d3748 !important;
                    margin-bottom: 4px !important;
                }}

                .signature-date {{
                    font-size: 12px !important;
                    color: #2d3748 !important;
                    font-weight: 500 !important;
                }}

                .closing {{
                    font-size: 12px !important;
                    margin-top: 20px !important;
                    color: #2d3748 !important;
                    line-height: 1.4 !important;
                }}

                /* 显示匹配度条形图 */
                .match-bar {{
                    width: 100% !important;
                    height: 8px !important;
                    background: #e2e8f0 !important;
                    border-radius: 4px !important;
                    overflow: hidden !important;
                    margin: 6px 0 !important;
                    position: relative !important;
                    -webkit-print-color-adjust: exact !important;
                    print-color-adjust: exact !important;
                    color-adjust: exact !important;
                }}

                .match-fill {{
                    height: 100% !important;
                    background: linear-gradient(90deg, #667eea, #764ba2) !important;
                    border-radius: 4px !important;
                    width: var(--match-percentage, 85%) !important;
                    position: relative !important;
                    -webkit-print-color-adjust: exact !important;
                    print-color-adjust: exact !important;
                    color-adjust: exact !important;
                }}

                /* 隐藏其他装饰性元素 */
                .progress-circle,
                .radar-chart {{
                    display: none !important;
                }}

                /* 优化文本段落间距 */
                p {{
                    margin: 3px 0 !important;
                    line-height: 1.3 !important;
                }}

                /* 确保内容不被分页截断 */
                .stats-section,
                .content > div {{
                    page-break-inside: avoid;
                    break-inside: avoid;
                }}

                /* 优化标题间距 */
                h1, h2, h3, h4, h5, h6 {{
                    margin: 4px 0 2px 0 !important;
                    line-height: 1.2 !important;
                }}

                /* 移除所有动画和过渡效果 */
                * {{
                    animation: none !important;
                    transition: none !important;
                    transform: none !important;
                }}

                /* 浏览器打印特殊优化 */
                html {{
                    -webkit-print-color-adjust: exact !important;
                    print-color-adjust: exact !important;
                    color-adjust: exact !important;
                }}

                /* 确保网格布局在打印时稳定 */
                .stats-grid {{
                    display: grid !important;
                    grid-template-columns: repeat(3, 1fr) !important;
                    width: 100% !important;
                    box-sizing: border-box !important;
                }}

                /* 强制显示所有边框和背景 */
                .stat-card, .stats-section {{
                    border-style: solid !important;
                    background-clip: padding-box !important;
                    -webkit-background-clip: padding-box !important;
                }}

                /* 文本渲染优化 */
                .stat-value, .stat-label, .stats-title {{
                    text-rendering: optimizeLegibility !important;
                    -webkit-font-smoothing: antialiased !important;
                    -moz-osx-font-smoothing: grayscale !important;
                }}
            }}

            /* 超小屏幕优化 - 仍保持三列 */
            @media (max-width: 480px) {{
                .stats-section {{
                    padding: 12px;
                    margin: 12px 0;
                }}

                .stats-grid {{
                    grid-template-columns: repeat(3, 1fr);
                    gap: 6px;
                }}

                .stat-card {{
                    padding: 8px 4px;
                }}

                .stat-value {{
                    font-size: 22px;
                }}

                .stat-label {{
                    font-size: 10px;
                }}
            }}
        </style>
    </head>
    <body>
        <!-- SVG定义，用于渐变和图形 -->
        <svg width="0" height="0" style="position: absolute;">
            <defs>
                <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="0%">
                    <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
                    <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
                </linearGradient>
                <filter id="glow">
                    <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
                    <feMerge>
                        <feMergeNode in="coloredBlur"/>
                        <feMergeNode in="SourceGraphic"/>
                    </feMerge>
                </filter>
            </defs>
        </svg>

        <div class="container">
            <div class="header">
                <div class="company-logo">
                    <img src="{logo_urls[0]}" alt="{company} Logo"
                         onerror="this.onerror=null; this.src='data:image/svg+xml,<svg xmlns=&quot;http://www.w3.org/2000/svg&quot; viewBox=&quot;0 0 24 24&quot; fill=&quot;%23ffffff&quot;><path d=&quot;M20 6L9 17l-5-5 1.41-1.41L9 14.17 18.59 7.41z&quot;/></svg>'">
                </div>
                <div class="header-title">To: {company}</div>
                <div class="header-subtitle">
                    <a href="{job_url}" target="_blank">{role}</a><span class="separator">•</span>职位申请<span class="separator">•</span>{location}
                </div>
            </div>

            <div class="content">
                {cover_letter_content}

                <div class="stats-section">
                    <div class="stats-title">📊 匹配度分析</div>
                    <div class="stats-grid">
                        <div class="stat-card">
                            <div class="stat-value">88%</div>
                            <div class="stat-label">技能匹配度</div>
                            <div class="match-bar">
                                <div class="match-fill" data-percentage="88" style="--match-percentage: 88%;"></div>
                            </div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-value">92%</div>
                            <div class="stat-label">经验相关性</div>
                            <div class="match-bar">
                                <div class="match-fill" data-percentage="92" style="--match-percentage: 92%;"></div>
                            </div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-value">95%</div>
                            <div class="stat-label">学习潜力</div>
                            <div class="match-bar">
                                <div class="match-fill" data-percentage="95" style="--match-percentage: 95%;"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="closing">
                    感谢您的时间和考虑。期待与您进一步交流的机会。
                </div>

                <div class="signature">
                    <div class="signature-name">{user_name}</div>
                    <div class="signature-date">{current_date}</div>
                </div>
            </div>

            <div class="footer">
                <p>此求职信由AIHawk智能求职助手生成 • 专业 • 个性化 • 高效</p>
            </div>
        </div>

        <script>
            // 页面加载完成后启动动画
            document.addEventListener('DOMContentLoaded', function() {{
                // 为每个统计卡片添加动画
                const statCards = document.querySelectorAll('.stat-card');
                statCards.forEach((card, index) => {{
                    setTimeout(() => {{
                        card.style.opacity = '0';
                        card.style.transform = 'translateY(20px)';
                        card.style.transition = 'all 0.6s ease';

                        setTimeout(() => {{
                            card.style.opacity = '1';
                            card.style.transform = 'translateY(0)';
                        }}, 100);
                    }}, index * 200);
                }});

                // 为匹配度条形图添加动画
                const matchBars = document.querySelectorAll('.match-fill');
                matchBars.forEach((bar, index) => {{
                    setTimeout(() => {{
                        const percentage = bar.getAttribute('data-percentage') || '85';
                        bar.style.setProperty('--match-percentage', percentage + '%');
                    }}, 1000 + index * 300);
                }});

                // 为圆形进度条添加动画
                const progressBars = document.querySelectorAll('.progress-bar');
                progressBars.forEach((bar, index) => {{
                    setTimeout(() => {{
                        const percentage = bar.getAttribute('data-percentage') || '85';
                        bar.style.setProperty('--progress', percentage);
                    }}, 1500 + index * 200);
                }});
            }});
        </script>
    </body>
    </html>
    """

    return html_template

def generate_optimized_resume_content(user_resume_data, job_info):
    """
    根据用户简历和职位信息生成优化的简历内容
    """
    try:
        api_key = global_config.API_KEY
        if not api_key:
            raise Exception("API密钥未配置")

        from src.libs.resume_and_cover_builder.llm.llm_job_parser import LLMParser
        llm_parser = LLMParser(api_key=api_key)

        # 提取用户信息
        personal_info = user_resume_data.get('personal_info', {})
        name = personal_info.get('name') or '[您的姓名]'
        email = personal_info.get('email') or '[您的邮箱]'
        phone = personal_info.get('phone') or '[您的电话]'
        address = personal_info.get('address') or '[您的地址]'

        # 处理技能数据 - 将嵌套结构转换为简单列表
        skills_data = user_resume_data.get('skills', {})
        processed_skills = []

        if isinstance(skills_data, dict):
            # 合并所有技能类别
            for skill_category, skill_list in skills_data.items():
                if isinstance(skill_list, list) and skill_list:
                    processed_skills.extend(skill_list)
                elif isinstance(skill_list, str) and skill_list:
                    processed_skills.append(skill_list)
        elif isinstance(skills_data, list):
            processed_skills = skills_data

        # 更新简历数据中的技能部分
        user_resume_data_processed = user_resume_data.copy()
        user_resume_data_processed['skills'] = processed_skills

        # 检测原始简历的语言
        def detect_resume_language(resume_data):
            """检测简历的主要语言"""
            # 检查个人信息和工作经验中的文本
            text_samples = []

            # 收集文本样本
            if isinstance(resume_data, dict):
                # 个人信息
                personal_info = resume_data.get('personal_info', {})
                if personal_info.get('name'):
                    text_samples.append(personal_info['name'])

                # 工作经验
                work_experience = resume_data.get('work_experience', [])
                for job in work_experience[:2]:  # 只检查前两个工作经验
                    if isinstance(job, dict):
                        if job.get('job_title'):
                            text_samples.append(job['job_title'])
                        if job.get('company'):
                            text_samples.append(job['company'])
                        if job.get('description'):
                            desc = job['description']
                            if isinstance(desc, str):
                                text_samples.append(desc[:200])  # 只取前200字符
                            elif isinstance(desc, list):
                                # 如果描述是列表，连接成字符串
                                desc_str = ' '.join([str(d) for d in desc if d])
                                text_samples.append(desc_str[:200])

                # 技能
                skills = resume_data.get('skills', [])
                if isinstance(skills, list) and skills:
                    # 确保技能是字符串类型
                    for skill in skills[:5]:
                        if isinstance(skill, str):
                            text_samples.append(skill)
                        elif isinstance(skill, list):
                            # 如果技能是列表，取第一个字符串元素
                            for s in skill:
                                if isinstance(s, str):
                                    text_samples.append(s)
                                    break

            # 分析文本样本，确保所有元素都是字符串
            text_samples = [str(sample) for sample in text_samples if sample]
            combined_text = ' '.join(text_samples)

            # 简单的语言检测逻辑
            chinese_chars = len([c for c in combined_text if '\u4e00' <= c <= '\u9fff'])
            total_chars = len(combined_text.replace(' ', ''))

            if total_chars == 0:
                return 'english'  # 默认英文

            chinese_ratio = chinese_chars / total_chars

            # 如果中文字符占比超过30%，认为是中文简历
            if chinese_ratio > 0.3:
                return 'chinese'
            else:
                return 'english'

        # 检测简历语言
        resume_language = detect_resume_language(user_resume_data_processed)
        print(f"检测到的简历语言: {resume_language}")

        # 根据语言选择提示模板
        if resume_language == 'chinese':
            # 中文提示
            optimization_prompt = f"""
            根据以下信息生成一份专业的HTML简历，针对目标职位进行优化。

            目标职位：{job_info.get('role', '未知职位')} - {job_info.get('company', '未知公司')}
            职位地点：{job_info.get('location', '未知地点')}

            用户信息：
            姓名：{name}
            邮箱：{email}
            电话：{phone}
            地址：{address}

            完整简历数据：
            {str(user_resume_data_processed)}"""
        else:
            # 英文提示
            optimization_prompt = f"""
            Generate a professional HTML resume optimized for the target position based on the following information.

            Target Position: {job_info.get('role', 'Unknown Position')} - {job_info.get('company', 'Unknown Company')}
            Location: {job_info.get('location', 'Unknown Location')}

            User Information:
            Name: {name}
            Email: {email}
            Phone: {phone}
            Address: {address}

            Complete Resume Data:
            {str(user_resume_data_processed)}"""

        # 添加通用的优化要求（根据语言调整）
        if resume_language == 'chinese':
            optimization_prompt += """

        **职位分析要求**：
        请仔细分析目标职位的核心要求，包括：
        - 所需的关键技能和经验（技术技能、软技能、行业知识等）
        - 行业背景要求（相关行业经验、专业知识等）
        - 职位层级要求（管理经验、领导能力、执行能力等）
        - 公司文化和价值观匹配点
        - 教育背景和认证要求
        - 语言能力和国际化经验（如适用）

        **核心使命**：将用户的简历转化为一份令人印象深刻、充满说服力的专业文档，让招聘者一眼就能看出候选人的价值和潜力。

        **重要提醒**：请仔细阅读用户的原始简历数据，只对真正需要优化的内容进行修改，并且只对修改过的部分添加ai-optimized标记。保持原始简历中已经合适的内容不变。

        重要要求：
        1. 直接返回完整的HTML代码，不要任何说明文字
        2. 使用用户的真实信息，但要**大胆地、创造性地**展现其价值
        3. **必须包含Summary部分**：
           - 在联系信息后添加Professional Summary或Summary部分
           - 根据目标职位要求，突出用户最相关的技能和经验
           - 长度控制在3-4句话，简洁有力
           - **关键要求**：必须包含1-2句具体说明用户为什么适合{job_info.get('company', '目标公司')}的{job_info.get('role', '目标职位')}职位
           - 匹配度分析要基于：
             * 用户的相关工作经验与职位要求的对应关系
             * 具体的技能匹配（如管理经验、技术能力、行业背景等）
             * 成就和结果导向的经验
           - 用简洁、自然、说人话的方式表达，避免空洞的套话
           - **示例格式**："具有X年相关经验的专业人士，专长于[具体技能/领域]。在[相关公司/行业]的工作经历使我具备了[目标职位所需的核心能力]，特别是在[具体匹配点]方面的丰富经验，正是[目标公司]寻找的[目标职位]候选人所需要的。"
           - **适应性要求**：根据不同行业和职位类型调整表述风格：
             * 技术职位：强调技术技能、项目经验、解决问题的能力
             * 管理职位：突出领导经验、团队管理、战略思维
             * 销售职位：重点展示业绩成果、客户关系、市场洞察
             * 创意职位：强调创新能力、作品集、创意思维
             * 金融职位：突出分析能力、风险管理、合规经验
           - **Skills部分示例**：
             ```html
             <div class="skills">
                 <span class="skill">项目管理</span>
                 <span class="skill">团队领导</span>
                 <span class="skill">数据分析</span>
             </div>
             ```
        4. **积极优化策略**：
           - 保持所有公司名称、时间段、职位名称完全一致
           - **智能扩充工作描述**：基于用户现有经验和目标职位要求，详细阐述相关工作内容和成就
           - **量化成果展示**：根据职位类型添加合理的量化指标（销售额、团队规模、项目数量、效率提升等）
           - **技能匹配优化**：深度挖掘用户经验中与目标职位相关的技能，并详细描述应用场景
           - **项目经验丰富**：扩展项目描述，包括技术栈、方法论、团队协作、解决方案等
           - **软技能强化**：根据职位要求突出相应的软技能（领导力、沟通能力、创新思维、分析能力等）
           - **行业术语优化**：使用目标行业和职位的专业术语，提升简历的专业度和匹配度
           - **成就导向转化**：将工作职责转化为具体成就、贡献和业务价值
           - **跨行业适应**：根据目标行业调整表述方式，突出可转移的技能和经验
        5. **高亮标记要求**：
           - **核心原则**：只标记真正被AI修改、优化、扩展或重新表述的内容
           - **标记判断标准**：
             * 如果内容与用户原始简历完全一致 → 不标记
             * 如果内容被重新表述、扩展、添加量化指标、调整措辞 → 必须标记
             * 如果内容是为了匹配目标职位而新增或修改的 → 必须标记
           - **具体标记范围**：
             * Professional Summary：完全标记（因为是基于职位要求新生成的）
             * 工作经验：只标记被优化/修改的句子或段落，保持原样的内容不标记
             * 技能部分：只标记重新排序、新增或重新表述的技能
             * 教育背景：通常保持原样，除非有针对性优化
           - **不需要标记的内容**：
             * 姓名、联系方式、公司名、工作时间等基本信息
             * 与原始简历完全一致且对目标职位无特殊帮助的描述
             * 未经任何修改的原始内容
           - **标记示例**：
             * 原始："负责招聘工作" → 优化："<span class='ai-optimized'>领导端到端招聘流程，成功招聘XX名人才</span>"
             * 原始："管理团队" → 优化："<span class='ai-optimized'>管理跨职能团队，提升协作效率</span>"
             * 原始："项目管理经验丰富" → 保持原样："项目管理经验丰富"（无需标记）
             * 原始："熟悉Excel" → 针对数据分析职位优化："<span class='ai-optimized'>精通Excel数据分析和建模</span>"
        6. **深度优化原则**：
           - **创造性扩展**：基于用户现有经验，创造性地扩展工作描述，添加合理的细节
           - **专业化表述**：将简单的工作描述转化为专业、详细的成就陈述
           - **技能深挖**：从工作经验中挖掘隐含的技能和能力，详细阐述
           - **影响力放大**：强调工作对公司、团队、项目的积极影响
           - **行业术语丰富**：大量使用目标行业和职位的专业术语
           - **数据驱动**：尽可能添加合理的量化数据（团队规模、项目周期、改进幅度等）
           - **故事化描述**：将工作经验描述得更加生动和具体
        7. **工作经验优化示例（适用于不同职位类型）**：
           - **HR/招聘类**："负责招聘工作" → "<span class='ai-optimized'>领导端到端招聘流程，包括需求分析、候选人筛选、面试协调和入职管理，成功为公司招聘了XX名优秀人才，显著提升了团队效率和人才质量</span>"
           - **管理类**："管理团队" → "<span class='ai-optimized'>管理跨职能团队，通过建立高效的沟通机制和绩效管理体系，提升团队协作效率，成功交付多个关键项目</span>"
           - **技术类**："开发系统" → "<span class='ai-optimized'>设计并开发高可用性系统，采用微服务架构和云原生技术，提升系统性能XX%，支撑日均XX万用户访问</span>"
           - **销售类**："销售产品" → "<span class='ai-optimized'>负责重点客户开发和维护，通过深度需求分析和定制化解决方案，实现销售额增长XX%，客户满意度达到XX%</span>"
           - **财务类**："财务分析" → "<span class='ai-optimized'>建立财务分析模型和风险控制体系，为管理层提供数据驱动的决策支持，优化成本结构，提升盈利能力XX%</span>"
        8. **精准标记检查清单**：
           在生成HTML之前，请逐一检查每个内容块：
           - ✅ Professional Summary：完全标记（新生成内容）
           - ✅ 工作经验：逐句比较原始简历，只标记被修改/扩展的部分
           - ✅ 技能部分：只标记重新排序或新增的技能
           - ✅ 量化数据：只标记新添加的数据和指标
           - ❌ 基本信息：姓名、联系方式、公司名、时间等不标记
           - ❌ 未修改内容：与原始简历完全一致的描述不标记
           **检查方法**：
           1. 对比原始简历内容
           2. 识别真正被修改的部分
           3. 只对修改部分添加 <span class="ai-optimized"> 标签
           4. 保持未修改内容的原始状态
        9. **绝对禁止**：
           - 添加不存在的工作经历或公司
           - 编造完全虚假的技能或经验
           - 修改公司名称、时间或职位标题
           - 完全虚构成就和数据（但可以添加合理的量化描述）
        10. **格式和重复内容要求**：
           - **文字格式**：确保所有单词之间有正确的空格，避免单词连在一起
           - **内容结构**：每个工作经历分为两部分：
             a) 简短的职位概述段落（2-3句话，描述整体职责和影响）
             b) 具体成就的bullet points列表（3-5个要点，展示具体成果）
           - **避免重复**：概述段落和bullet points必须包含不同的信息，不能重复相同内容
           - **示例结构**：
             ```html
             <div class="job">
                 <div class="job-header">
                     <div class="job-title">职位名称</div>
                     <div class="date">时间</div>
                 </div>
                 <div class="company">公司名称</div>
                 <p><span class="ai-optimized">职位概述段落，描述整体职责和影响</span></p>
                 <ul>
                     <li><span class="ai-optimized">具体成就1，包含量化指标</span></li>
                     <li><span class="ai-optimized">具体成就2，展示技能应用</span></li>
                     <li><span class="ai-optimized">具体成就3，体现业务价值</span></li>
                 </ul>
             </div>
             ```
        11. **Skills部分格式要求**：
           - **关键要求**：必须使用正确的HTML结构：<div class="skills"><span class="skill">技能名称</span></div>
           - 每个技能用单独的<span class="skill">标签包装
           - 技能名称要简洁明了，避免过长的描述
           - **重要**：确保每个<span class="skill">标签内都有实际的技能文本，不能为空
           - **数据处理说明**：用户的技能数据已经被处理成简单的技能列表，请直接使用这些技能
           - **智能技能筛选**：根据目标职位要求，优先显示最相关的技能
           - **技能分类优化**：根据职位类型调整技能展示重点：
             * 技术职位：编程语言、框架、工具、平台
             * 管理职位：领导力、战略规划、团队建设、项目管理
             * 销售职位：客户关系、谈判技巧、市场分析、CRM系统
             * 创意职位：设计软件、创意思维、用户体验、品牌策略
             * 分析职位：数据分析、统计软件、建模技能、可视化工具
           - **必须避免**：
             * 空的技能标签：<span class="skill"></span>
             * 只有空格的技能标签：<span class="skill">   </span>
             * 无效的技能内容
           - **示例格式**：
             ```html
             <div class="section">
                 <div class="section-title">Skills</div>
                 <div class="skills">
                     <span class="skill">项目管理</span>
                     <span class="skill">团队领导</span>
                     <span class="skill">数据分析</span>
                     <span class="skill">BrassRing</span>
                     <span class="skill">SHL Assessment Center</span>
                 </div>
             </div>
             ```
        12. **Education部分格式要求**：
           - 必须使用专门的HTML结构，类似工作经验的布局
           - 每个教育经历使用以下结构：
             ```html
             <div class="education">
                 <div class="education-header">
                     <div class="degree">学位名称, 专业</div>
                     <div class="education-date">毕业时间</div>
                 </div>
                 <div class="institution">学校名称</div>
             </div>
             ```
           - degree包含学位和专业信息
           - institution显示学校名称
           - education-date显示毕业时间
        13. **Certifications部分格式要求**：
           - 保持简单的文本格式，不要使用Skills的标签样式
           - 每个证书单独一行，使用普通的<p>或<div>标签
           - 格式：证书名称 + 获得时间（如果有的话）
           - 不要添加特殊的CSS类或样式
        14. **工作经验扩展指导**：
           - **内容丰富度**：每个工作经历至少包含3-5个详细的成就描述
           - **动作词使用**：根据职位类型选择合适的动作词：
             * 管理类：领导、管理、协调、规划、决策、监督
             * 技术类：开发、设计、实施、优化、构建、维护
             * 销售类：开拓、维护、谈判、达成、提升、扩展
             * 分析类：分析、评估、建模、预测、优化、改进
           - **工具和方法**：包含具体的工作方法、使用的工具、技术栈、协作平台
           - **问题解决**：强调解决的具体问题、克服的挑战、创新的解决方案
           - **价值创造**：量化展示为公司、团队、项目创造的具体价值和影响
           - **成长体现**：体现持续学习、技能提升、职业发展的轨迹
        15. **通用性和适应性要求**：
           - **行业无关性**：确保优化策略适用于各种行业（科技、金融、制造、教育、医疗、零售等）
           - **职位层级适应**：根据职位层级调整表述（入门级、中级、高级、管理层、高管）
           - **公司规模适应**：考虑目标公司规模（初创公司、中小企业、大型企业、跨国公司）
           - **文化背景适应**：适应不同的企业文化和价值观（创新型、传统型、国际化等）
           - **技能可转移性**：突出跨行业、跨职能的可转移技能
           - **语言本地化**：根据职位地点和公司背景调整语言风格
        16. **样式一致性要求**：
           - 不要在HTML中生成任何高亮切换按钮或相关的JavaScript代码
           - 不要修改现有的CSS样式定义
           - 确保每次生成的页面布局和样式保持一致
           - 只在内容中添加ai-optimized类，不要修改其他样式
           - 高亮功能将由系统自动添加，请勿在生成的HTML中包含
        17. **最终检查要求**：
           生成HTML后，请进行精准检查：
           - **对比检查**：将生成的内容与用户原始简历逐一对比
           - **精准标记**：只对真正被修改、扩展、重新表述的内容添加 <span class="ai-optimized"> 标签
           - **保持原样**：与原始简历完全一致的内容保持原始状态，不添加任何标记
           - **避免过度标记**：不要因为内容出现在优化简历中就自动标记，要确认是否真的被修改过
           - **Professional Summary例外**：由于是完全新生成的内容，整个Summary部分都需要标记
        18. 专业美观的格式

        请直接返回HTML代码，格式如下：

        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <title>{name} - 简历</title>
            <style>
                * {{ box-sizing: border-box; }}
                body {{
                    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                    margin: 0;
                    padding: 20px;
                    background-color: #f5f5f5;
                    line-height: 1.6;
                }}
                .resume {{
                    max-width: 800px;
                    margin: 0 auto;
                    background: white;
                    padding: 40px;
                    box-shadow: 0 0 10px rgba(0,0,0,0.1);
                    word-wrap: break-word;
                    overflow-wrap: break-word;
                }}
                .header {{
                    text-align: center;
                    border-bottom: 2px solid #2c3e50;
                    padding-bottom: 20px;
                    margin-bottom: 30px;
                }}
                .name {{
                    font-size: 2.2em;
                    font-weight: bold;
                    color: #2c3e50;
                    margin-bottom: 10px;
                    word-break: break-word;
                }}
                .contact {{
                    color: #7f8c8d;
                    font-size: 1em;
                    word-break: break-all;
                }}
                .summary {{
                    font-size: 1.1em;
                    line-height: 1.7;
                    color: #34495e;
                    text-align: justify;
                    margin-bottom: 5px;
                    word-wrap: break-word;
                    overflow-wrap: break-word;
                }}
                .section {{
                    margin-bottom: 25px;
                    clear: both;
                }}
                .section-title {{
                    font-size: 1.4em;
                    font-weight: bold;
                    color: #2c3e50;
                    border-bottom: 1px solid #bdc3c7;
                    padding-bottom: 5px;
                    margin-bottom: 15px;
                }}
                .job {{
                    margin-bottom: 20px;
                    overflow: hidden;
                    clear: both;
                }}
                .job-header {{
                    display: flex;
                    justify-content: space-between;
                    align-items: flex-start;
                    flex-wrap: wrap;
                    margin-bottom: 5px;
                }}
                .job-title {{
                    font-weight: bold;
                    color: #34495e;
                    flex: 1;
                    min-width: 0;
                    word-break: break-word;
                }}
                .company {{
                    color: #7f8c8d;
                    font-style: italic;
                    margin-top: 2px;
                    word-break: break-word;
                }}
                .date {{
                    color: #95a5a6;
                    font-size: 0.9em;
                    white-space: nowrap;
                    margin-left: 10px;
                }}
                .description {{
                    margin-top: 8px;
                    text-align: justify;
                    word-wrap: break-word;
                    overflow-wrap: break-word;
                }}
                .skills {{
                    display: flex;
                    flex-wrap: wrap;
                    gap: 8px;
                    margin-top: 10px;
                }}
                .skill, .skills span {{
                    background: #3498db !important;
                    color: #ffffff !important;
                    padding: 6px 12px;
                    border-radius: 15px;
                    font-size: 0.9em;
                    font-weight: 500;
                    white-space: nowrap;
                    border: 1px solid #3498db;
                    transition: all 0.2s ease;
                    display: inline-block;
                    text-decoration: none;
                    margin: 2px;
                }}
                .skill:hover {{
                    background: #2980b9;
                    border-color: #2980b9;
                    transform: translateY(-1px);
                    box-shadow: 0 2px 4px rgba(52, 152, 219, 0.3);
                }}
                /* Education部分样式 */
                .education {{
                    margin-bottom: 20px;
                    overflow: hidden;
                    clear: both;
                }}
                .education-header {{
                    display: flex;
                    justify-content: space-between;
                    align-items: flex-start;
                    flex-wrap: wrap;
                    margin-bottom: 5px;
                }}
                .degree {{
                    font-weight: bold;
                    color: #34495e;
                    flex: 1;
                    min-width: 0;
                    word-break: break-word;
                }}
                .institution {{
                    color: #7f8c8d;
                    font-style: italic;
                    margin-top: 2px;
                    word-break: break-word;
                }}
                .education-date {{
                    color: #95a5a6;
                    font-size: 0.9em;
                    white-space: nowrap;
                    margin-left: 10px;
                }}
                /* 高亮优化内容的样式 - 仅在预览中显示 */
                .ai-optimized {{
                    background-color: rgba(25, 118, 210, 0.12);
                    border-left: 3px solid #1976d2;
                    padding: 2px 6px 2px 8px;
                    margin: 2px 0;
                    border-radius: 4px;
                    position: relative;
                    display: inline;
                    box-shadow: 0 1px 3px rgba(25, 118, 210, 0.1);
                }}
                .ai-optimized::before {{
                    content: "✨";
                    position: absolute;
                    left: -18px;
                    top: 0;
                    font-size: 12px;
                    z-index: 10;
                    opacity: 0.8;
                    transition: opacity 0.2s ease;
                    line-height: 1;
                }}
                .ai-optimized:hover::before {{
                    opacity: 1;
                }}
                /* 隐藏高亮时的样式 */
                .ai-optimized-hidden {{
                    background-color: transparent !important;
                    border-left: none !important;
                    padding: 0 !important;
                    margin: 0 !important;
                    box-shadow: none !important;
                }}
                .ai-optimized-hidden::before {{
                    display: none !important;
                }}
                /* 高亮切换按钮样式 */
                .highlight-toggle {{
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    z-index: 1000;
                    background: white;
                    border-radius: 8px;
                    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                    padding: 4px;
                }}
                .highlight-toggle button {{
                    background: linear-gradient(135deg, #1976d2, #1565c0);
                    color: white;
                    border: none;
                    padding: 8px 12px;
                    border-radius: 6px;
                    cursor: pointer;
                    font-size: 0.8em;
                    font-weight: 500;
                    transition: all 0.2s ease;
                    box-shadow: 0 2px 4px rgba(25, 118, 210, 0.3);
                }}
                .highlight-toggle button:hover {{
                    background: linear-gradient(135deg, #1565c0, #0d47a1);
                    transform: translateY(-1px);
                    box-shadow: 0 4px 8px rgba(25, 118, 210, 0.4);
                }}
                .highlight-toggle button:active {{
                    transform: translateY(0);
                }}
                /* PDF生成时隐藏高亮样式 */
                @media print {{
                    .ai-optimized {{
                        background-color: transparent !important;
                        border-left: none !important;
                        padding: 0 !important;
                        margin: 0 !important;
                        box-shadow: none !important;
                    }}
                    .ai-optimized::before {{
                        display: none !important;
                    }}
                    .highlight-toggle {{
                        display: none !important;
                    }}
                }}
                @media (max-width: 600px) {{
                    .resume {{ padding: 20px; }}
                    .name {{ font-size: 1.8em; }}
                    .contact {{ font-size: 0.9em; }}
                    .job-header {{ flex-direction: column; }}
                    .date {{ margin-left: 0; margin-top: 5px; }}
                    .education-header {{ flex-direction: column; }}
                    .education-date {{ margin-left: 0; margin-top: 5px; }}
                }}
            </style>
        </head>
        <body>
            <div class="resume">
                <div class="header">
                    <div class="name">{name}</div>
                    <div class="contact">{phone} | {email} | {address}</div>
                </div>

                <div class="section">
                    <div class="section-title">Professional Summary</div>
                    <div class="summary">
                        <!-- AI将在这里生成针对目标职位的专业总结 -->
                    </div>
                </div>

                <div class="section">
                    <div class="section-title">Work Experience</div>
                    <!-- AI将在这里生成优化后的工作经历 -->
                </div>

                <div class="section">
                    <div class="section-title">Education</div>
                    <!-- AI将在这里生成教育背景 -->
                </div>

                <div class="section">
                    <div class="section-title">Skills</div>
                    <div class="skills">
                        <!-- AI将在这里生成技能标签，格式如：<span class="skill">技能名称</span> -->
                    </div>
                </div>
            </div>
        </body>
        </html>
        """
        else:
            # 英文提示
            optimization_prompt += """

        **Position Analysis Requirements**:
        Please carefully analyze the core requirements of the target position, including:
        - Required key skills and experience (technical skills, soft skills, industry knowledge, etc.)
        - Industry background requirements (relevant industry experience, professional knowledge, etc.)
        - Position level requirements (management experience, leadership skills, execution ability, etc.)
        - Company culture and value alignment
        - Educational background and certification requirements
        - Language skills and international experience (if applicable)

        **Core Mission**: Transform the user's resume into an impressive, persuasive professional document that allows recruiters to immediately see the candidate's value and potential.

        **Important Reminder**: Please carefully read the user's original resume data, only modify content that truly needs optimization, and only add ai-optimized tags to modified parts. Keep content that is already appropriate in the original resume unchanged.

        Important Requirements:
        1. Return complete HTML code directly, without any explanatory text
        2. Use the user's real information, but **boldly and creatively** showcase their value
        3. **Must include Summary section**:
           - Add Professional Summary or Summary section after contact information
           - Based on target position requirements, highlight user's most relevant skills and experience
           - Keep length to 3-4 sentences, concise and powerful
           - **Key requirement**: Must include 1-2 sentences specifically explaining why the user is suitable for the {job_info.get('role', 'target position')} position at {job_info.get('company', 'target company')}
           - Match analysis should be based on:
             * Correspondence between user's relevant work experience and position requirements
             * Specific skill matches (management experience, technical abilities, industry background, etc.)
             * Achievement and result-oriented experience
           - Express in concise, natural, human language, avoiding empty clichés
           - **Example format**: "Professional with X years of relevant experience, specializing in [specific skills/areas]. Work experience at [relevant companies/industries] has equipped me with [core capabilities required for target position], particularly rich experience in [specific match points], which is exactly what [target company] needs for their [target position] candidate."

        4. **Active Optimization Strategy**:
           - Keep all company names, time periods, and position titles completely consistent
           - **Intelligent work description expansion**: Based on user's existing experience and target position requirements, elaborate on relevant work content and achievements
           - **Quantified results display**: Add reasonable quantitative indicators based on position type (sales volume, team size, project count, efficiency improvement, etc.)
           - **Skill matching optimization**: Deeply explore skills related to target position from user experience and describe application scenarios in detail
           - **Rich project experience**: Expand project descriptions including tech stack, methodology, team collaboration, solutions, etc.
           - **Soft skills enhancement**: Highlight corresponding soft skills based on position requirements (leadership, communication, innovative thinking, analytical ability, etc.)
           - **Industry terminology optimization**: Use professional terminology from target industry and position to enhance resume professionalism and matching
           - **Achievement-oriented transformation**: Transform work responsibilities into specific achievements, contributions, and business value
           - **Cross-industry adaptation**: Adjust expression methods based on target industry, highlighting transferable skills and experience

        5. **Work Experience Format Requirements**:
           - **Content Structure**: Each work experience should be divided into two parts:
             a) Brief position overview paragraph (2-3 sentences describing overall responsibilities and impact)
             b) Specific achievement bullet points list (3-5 points showing concrete results)
           - **Avoid Repetition**: Overview paragraph and bullet points must contain different information, cannot repeat the same content
           - **Example Structure**:
             ```html
             <div class="job">
                 <div class="job-header">
                     <div class="job-title">Position Title</div>
                     <div class="date">Time Period</div>
                 </div>
                 <div class="company">Company Name</div>
                 <p><span class="ai-optimized">Position overview paragraph describing overall responsibilities and impact</span></p>
                 <ul>
                     <li><span class="ai-optimized">Specific achievement 1 with quantitative indicators</span></li>
                     <li><span class="ai-optimized">Specific achievement 2 showing skill application</span></li>
                     <li><span class="ai-optimized">Specific achievement 3 demonstrating business value</span></li>
                 </ul>
             </div>
             ```

        5. **Skills Section Format Requirements - CRITICAL FOR ENGLISH RESUMES**:
           - **MANDATORY HTML Structure - MUST FOLLOW EXACTLY**:
             ```html
             <div class="section">
                 <div class="section-title">Skills</div>
                 <div class="skills">
                     <span class="skill">MS Excel</span>
                     <span class="skill">MS PowerPoint</span>
                     <span class="skill">Communication</span>
                     <span class="skill">Leadership</span>
                     <span class="skill">Creativity</span>
                     <span class="skill">Detail-oriented</span>
                     <span class="skill">Video Editing</span>
                     <span class="skill">Photoshop</span>
                 </div>
             </div>
             ```
           - **ABSOLUTE REQUIREMENTS - NO EXCEPTIONS**:
             * Each skill MUST be wrapped in <span class="skill">Skill Name</span>
             * NO empty tags: <span class="skill"></span> is STRICTLY FORBIDDEN
             * NO whitespace-only tags: <span class="skill">   </span> is STRICTLY FORBIDDEN
             * Skills MUST be single words or short phrases (max 3-4 words)
             * Use EXACT class names: "skills" for container, "skill" for individual items
             * EVERY skill tag MUST contain actual text content

           - **Skill Selection Strategy for English Resumes**:
             * Extract skills from user's original resume data first
             * Prioritize skills most relevant to target position
             * Include both technical and soft skills
             * Limit to 15-20 skills maximum for optimal display
             * For Brand Ambassador positions: Communication, Leadership, Marketing, Event Management
             * For Business Development: Strategic Planning, Market Analysis, Client Relations, Negotiation

           - **MANDATORY Quality Control for English Resumes**:
             * VERIFY each skill tag contains actual text before generating
             * REMOVE any empty or invalid tags immediately
             * ENSURE proper HTML structure is maintained
             * TEST that skills display as blue rounded tags
             * DOUBLE-CHECK that no skill tags are empty

           - **Position-Specific Skill Focus for English Resumes**:
             * Marketing/Brand: Brand Management, Marketing Strategy, Social Media, Event Planning
             * Business Development: Strategic Planning, Market Analysis, Client Relations, Negotiation
             * Technical: Programming Languages, Frameworks, Development Tools, Software Proficiency
             * Management: Leadership, Strategic Planning, Team Building, Project Management
             * Sales: Customer Relations, CRM, Negotiation, Market Analysis

           - **CRITICAL VALIDATION CHECKLIST for English Resumes**:
             ✓ Skills section has proper HTML structure with <div class="skills">
             ✓ Each skill is in <span class="skill">actual_text</span> format
             ✓ NO empty skill tags exist: <span class="skill"></span>
             ✓ NO whitespace-only skill tags exist: <span class="skill">   </span>
             ✓ Skills are relevant to the position and extracted from resume
             ✓ Skills display as blue rounded tags in UI
             ✓ At least 8-15 valid skills are included

        6. **Highlighting Requirements**:
           - **Core principle**: Only mark content that was truly modified, optimized, expanded, or rephrased by AI
           - **Marking criteria**:
             * If content is completely identical to user's original resume → Don't mark
             * If content was rephrased, expanded, added quantitative indicators, or adjusted wording → Must mark
             * If content was added or modified to match target position → Must mark
           - **Specific marking scope**:
             * Professional Summary: Fully mark (because it's newly generated based on position requirements)
             * Work experience: Only mark optimized/modified sentences or paragraphs, keep unchanged content unmarked
             * Skills section: Only mark reordered, added, or rephrased skills
             * Educational background: Usually keep original, unless specifically optimized
           - **Content that doesn't need marking**:
             * Name, contact information, company names, work times, and other basic information
             * Descriptions completely identical to original resume that don't specifically help with target position
             * Unmodified original content
           - **Marking examples**:
             * Original: "Responsible for recruitment" → Optimized: "<span class='ai-optimized'>Led end-to-end recruitment process, successfully recruited XX talented individuals</span>"
             * Original: "Managed team" → Optimized: "<span class='ai-optimized'>Managed cross-functional team, improved collaboration efficiency</span>"
             * Original: "Rich project management experience" → Keep original: "Rich project management experience" (no marking needed)
             * Original: "Familiar with Excel" → For data analysis position: "<span class='ai-optimized'>Proficient in Excel data analysis and modeling</span>"

        Please return HTML code directly in the following format:

        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <title>{name} - Resume</title>
            <style>
                * {{ box-sizing: border-box; }}
                body {{
                    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                    margin: 0;
                    padding: 20px;
                    background-color: #f5f5f5;
                    line-height: 1.6;
                }}
                .resume {{
                    max-width: 800px;
                    margin: 0 auto;
                    background: white;
                    padding: 40px;
                    box-shadow: 0 0 10px rgba(0,0,0,0.1);
                    word-wrap: break-word;
                    overflow-wrap: break-word;
                }}
                .header {{
                    text-align: center;
                    border-bottom: 2px solid #2c3e50;
                    padding-bottom: 20px;
                    margin-bottom: 30px;
                }}
                .name {{
                    font-size: 2.2em;
                    font-weight: bold;
                    color: #2c3e50;
                    margin-bottom: 10px;
                    word-break: break-word;
                }}
                .contact {{
                    color: #7f8c8d;
                    font-size: 1em;
                    word-break: break-all;
                }}
                .summary {{
                    font-size: 1.1em;
                    line-height: 1.7;
                    color: #34495e;
                    text-align: justify;
                    margin-bottom: 5px;
                    word-wrap: break-word;
                    overflow-wrap: break-word;
                }}
                .section {{
                    margin-bottom: 25px;
                    clear: both;
                }}
                .section-title {{
                    font-size: 1.4em;
                    font-weight: bold;
                    color: #2c3e50;
                    border-bottom: 1px solid #bdc3c7;
                    padding-bottom: 5px;
                    margin-bottom: 15px;
                }}
                .job {{
                    margin-bottom: 20px;
                    overflow: hidden;
                    clear: both;
                }}
                .job-header {{
                    display: flex;
                    justify-content: space-between;
                    align-items: flex-start;
                    flex-wrap: wrap;
                    margin-bottom: 5px;
                }}
                .job-title {{
                    font-weight: bold;
                    color: #34495e;
                    flex: 1;
                    min-width: 0;
                    word-break: break-word;
                }}
                .company {{
                    color: #7f8c8d;
                    font-style: italic;
                    margin-top: 2px;
                    word-break: break-word;
                }}
                .date {{
                    color: #95a5a6;
                    font-size: 0.9em;
                    white-space: nowrap;
                    margin-left: 10px;
                }}
                .description {{
                    margin-top: 8px;
                    text-align: justify;
                    word-wrap: break-word;
                    overflow-wrap: break-word;
                }}
                .skills {{
                    display: flex;
                    flex-wrap: wrap;
                    gap: 8px;
                    margin-top: 10px;
                }}
                .skill, .skills span {{
                    background: #3498db !important;
                    color: #ffffff !important;
                    padding: 6px 12px;
                    border-radius: 15px;
                    font-size: 0.9em;
                    font-weight: 500;
                    white-space: nowrap;
                    border: 1px solid #3498db;
                    transition: all 0.2s ease;
                    display: inline-block;
                    text-decoration: none;
                    margin: 2px;
                }}
                .skill:hover {{
                    background: #2980b9;
                    border-color: #2980b9;
                    transform: translateY(-1px);
                    box-shadow: 0 2px 4px rgba(52, 152, 219, 0.3);
                }}
                .education {{
                    margin-bottom: 20px;
                    overflow: hidden;
                    clear: both;
                }}
                .education-header {{
                    display: flex;
                    justify-content: space-between;
                    align-items: flex-start;
                    flex-wrap: wrap;
                    margin-bottom: 5px;
                }}
                .degree {{
                    font-weight: bold;
                    color: #34495e;
                    flex: 1;
                    min-width: 0;
                    word-break: break-word;
                }}
                .institution {{
                    color: #7f8c8d;
                    font-style: italic;
                    margin-top: 2px;
                    word-break: break-word;
                }}
                .education-date {{
                    color: #95a5a6;
                    font-size: 0.9em;
                    white-space: nowrap;
                    margin-left: 10px;
                }}
                .ai-optimized {{
                    background-color: rgba(25, 118, 210, 0.12);
                    border-left: 3px solid #1976d2;
                    padding: 2px 6px 2px 8px;
                    margin: 2px 0;
                    border-radius: 4px;
                    position: relative;
                    display: inline;
                    box-shadow: 0 1px 3px rgba(25, 118, 210, 0.1);
                }}
                .ai-optimized::before {{
                    content: "✨";
                    position: absolute;
                    left: -18px;
                    top: 0;
                    font-size: 12px;
                    z-index: 10;
                    opacity: 0.8;
                    transition: opacity 0.2s ease;
                    line-height: 1;
                }}
                .ai-optimized:hover::before {{
                    opacity: 1;
                }}
                /* 隐藏高亮时的样式 */
                .ai-optimized-hidden {{
                    background-color: transparent !important;
                    border-left: none !important;
                    padding: 0 !important;
                    margin: 0 !important;
                    box-shadow: none !important;
                }}
                .ai-optimized-hidden::before {{
                    display: none !important;
                }}
                /* 高亮切换按钮样式 */
                .highlight-toggle {{
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    z-index: 1000;
                    background: white;
                    border-radius: 8px;
                    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                    padding: 4px;
                }}
                .highlight-toggle button {{
                    background: linear-gradient(135deg, #1976d2, #1565c0);
                    color: white;
                    border: none;
                    padding: 8px 12px;
                    border-radius: 6px;
                    cursor: pointer;
                    font-size: 0.8em;
                    font-weight: 500;
                    transition: all 0.2s ease;
                    box-shadow: 0 2px 4px rgba(25, 118, 210, 0.3);
                }}
                .highlight-toggle button:hover {{
                    background: linear-gradient(135deg, #1565c0, #0d47a1);
                    transform: translateY(-1px);
                    box-shadow: 0 4px 8px rgba(25, 118, 210, 0.4);
                }}
                .highlight-toggle button:active {{
                    transform: translateY(0);
                }}
                @media print {{
                    .ai-optimized {{
                        background-color: transparent !important;
                        border-left: none !important;
                        padding: 0 !important;
                        margin: 0 !important;
                        box-shadow: none !important;
                    }}
                    .ai-optimized::before {{
                        display: none !important;
                    }}
                    .highlight-toggle {{
                        display: none !important;
                    }}
                }}
                @media (max-width: 600px) {{
                    .resume {{ padding: 20px; }}
                    .name {{ font-size: 1.8em; }}
                    .contact {{ font-size: 0.9em; }}
                    .job-header {{ flex-direction: column; }}
                    .date {{ margin-left: 0; margin-top: 5px; }}
                    .education-header {{ flex-direction: column; }}
                    .education-date {{ margin-left: 0; margin-top: 5px; }}
                }}
            </style>
        </head>
        <body>
            <div class="resume">
                <div class="header">
                    <div class="name">{name}</div>
                    <div class="contact">{phone} | {email} | {address}</div>
                </div>

                <div class="section">
                    <div class="section-title">Professional Summary</div>
                    <div class="summary">
                        <!-- AI will generate professional summary targeting the position here -->
                    </div>
                </div>

                <div class="section">
                    <div class="section-title">Work Experience</div>
                    <!-- AI will generate optimized work experience here -->
                </div>

                <div class="section">
                    <div class="section-title">Education</div>
                    <!-- AI will generate educational background here -->
                </div>

                <div class="section">
                    <div class="section-title">Skills</div>
                    <div class="skills">
                        <!-- AI MUST generate skill tags here in this EXACT format: <span class="skill">Skill Name</span> -->
                        <!-- EXAMPLE: <span class="skill">MS Excel</span><span class="skill">Communication</span><span class="skill">Leadership</span> -->
                        <!-- NO EMPTY TAGS ALLOWED: <span class="skill"></span> is FORBIDDEN -->
                    </div>
                </div>

                <div class="section">
                    <div class="section-title">Certifications</div>
                    <!-- AI will generate certifications here -->
                </div>
            </div>
        </body>
        </html>
        """

        # 使用LLM生成优化简历
        if hasattr(llm_parser.llm, 'invoke'):
            response = llm_parser.llm.invoke(optimization_prompt)
        elif hasattr(llm_parser.llm, 'predict'):
            response = llm_parser.llm.predict(optimization_prompt)
        elif hasattr(llm_parser.llm, 'llm') and hasattr(llm_parser.llm.llm, 'invoke'):
            response = llm_parser.llm.llm.invoke(optimization_prompt)
        else:
            raise Exception("无法调用LLM模型")

        # 提取响应内容
        if hasattr(response, 'content'):
            optimized_content = response.content
        else:
            optimized_content = str(response)

        # 清理HTML内容
        cleaned_content = clean_html_content(optimized_content)

        # 验证并修复Skills部分
        validated_content = validate_and_fix_skills_section(cleaned_content, user_resume_data_processed)

        return validated_content

    except Exception as e:
        logger.error(f"简历优化失败: {str(e)}")
        raise e

def validate_and_fix_skills_section(html_content, user_resume_data=None):
    """
    验证并修复Skills部分的HTML格式，确保稳定输出
    """
    import re
    from bs4 import BeautifulSoup

    try:
        # 解析HTML
        soup = BeautifulSoup(html_content, 'html.parser')

        # 查找Skills部分 - 遍历所有section找到Skills
        skills_section = None
        skills_container = None

        # 查找所有section
        all_sections = soup.find_all('div', class_='section')
        for section in all_sections:
            section_title = section.find('div', class_='section-title')
            if section_title and 'Skills' in section_title.get_text():
                skills_section = section
                skills_container = section.find('div', class_='skills')
                break

        if skills_section:

                if not skills_container:
                    # 如果没有skills容器，创建一个
                    skills_container = soup.new_tag('div', **{'class': 'skills'})
                    # 找到Skills标题并在其后插入容器
                    section_title = skills_section.find('div', class_='section-title')
                    if section_title:
                        section_title.insert_after(skills_container)

                # 检查现有的skill标签
                skill_spans = skills_container.find_all('span', class_='skill')
                valid_skills = []

                # 验证现有技能
                for span in skill_spans:
                    skill_text = span.get_text().strip()
                    if skill_text and len(skill_text) > 0:
                        valid_skills.append(skill_text)

                # 如果技能太少或为空，从用户简历数据中补充
                if len(valid_skills) < 5 and user_resume_data:
                    fallback_skills = extract_fallback_skills(user_resume_data)
                    valid_skills.extend(fallback_skills[:15-len(valid_skills)])

                # 重建skills容器
                skills_container.clear()
                for skill in valid_skills[:20]:  # 限制最多20个技能
                    if skill and skill.strip():
                        skill_span = soup.new_tag('span', **{'class': 'skill'})
                        skill_span.string = skill.strip()
                        skills_container.append(skill_span)

        return str(soup)

    except Exception as e:
        print(f"Skills验证修复失败: {str(e)}")
        return html_content

def extract_fallback_skills(user_resume_data):
    """
    从用户简历数据中提取备用技能列表
    """
    fallback_skills = []

    try:
        if user_resume_data and 'skills' in user_resume_data:
            skills_data = user_resume_data['skills']

            # 提取各类技能
            if 'soft_skills' in skills_data:
                fallback_skills.extend(skills_data['soft_skills'][:10])

            if 'tools' in skills_data:
                fallback_skills.extend(skills_data['tools'][:8])

            if 'programming_languages' in skills_data:
                fallback_skills.extend(skills_data['programming_languages'][:5])

            if 'frameworks' in skills_data:
                fallback_skills.extend(skills_data['frameworks'][:5])

        # 如果还是没有技能，提供通用的英文技能
        if not fallback_skills:
            fallback_skills = [
                "MS Excel", "MS PowerPoint", "Communication", "Leadership",
                "Creativity", "Detail-oriented", "Video Editing", "Photoshop",
                "Project Management", "Team Leadership", "Strategic Planning",
                "Data Analysis", "Problem Solving", "Time Management",
                "Customer Service", "Marketing", "Social Media", "Event Planning"
            ]

    except Exception as e:
        print(f"提取备用技能失败: {str(e)}")
        # 返回默认技能列表
        fallback_skills = [
            "Project Management", "Team Leadership", "Communication",
            "Problem Solving", "Strategic Planning", "Data Analysis"
        ]

    return fallback_skills

def clean_html_content(html_content):
    """
    清理LLM生成的HTML内容，去除多余的说明文字
    """
    import re

    # 去除开头的说明文字
    patterns_to_remove = [
        r'^.*?(?=<!DOCTYPE|<html)',  # 去除HTML开始前的所有文字
        r'```html\s*',  # 去除```html标记
        r'```\s*$',     # 去除结尾的```标记
        r'\*\*.*?\*\*',  # 去除**包围的文字
        r'CSS.*?(?=</style>)',  # 去除CSS说明文字
        r'```css.*?```',  # 去除CSS代码块
    ]

    cleaned = html_content
    for pattern in patterns_to_remove:
        cleaned = re.sub(pattern, '', cleaned, flags=re.DOTALL | re.MULTILINE)

    # 确保HTML结构完整
    if not cleaned.strip().startswith('<!DOCTYPE') and not cleaned.strip().startswith('<html'):
        # 如果没有完整的HTML结构，提取HTML部分
        html_match = re.search(r'<!DOCTYPE.*?</html>', cleaned, re.DOTALL)
        if html_match:
            cleaned = html_match.group(0)

    return cleaned.strip()

def validate_api_key():
    """验证API密钥的有效性和类型"""
    if not global_config.API_KEY:
        print("错误: API密钥未配置")
        return False, "API密钥未配置，请检查secrets.yaml文件"
    
    # 检查API密钥格式（Gemini API密钥通常以'AIza'开头）
    if not global_config.API_KEY.startswith('AIza'):
        print("错误: API密钥格式不正确，请确保使用有效的Gemini API密钥")
        return False, "API密钥格式不正确，请确保使用有效的Gemini API密钥"
    
    return True, ""

class ResumeGenerateRequest(BaseModel):
    job_url: str
    user_resume: Optional[dict] = None

class CoverLetterGenerateRequest(BaseModel):
    job_info: dict
    optimized_resume_html: str

@app.post("/api/resume/upload")
async def upload_resume(resume: UploadFile = File(...)):
    """
    上传并解析用户简历
    """
    try:
        # 验证文件类型
        if resume.content_type != "application/pdf":
            return {"status": "error", "message": "只支持PDF格式的简历文件"}

        # 读取文件内容
        content = await resume.read()

        # 解析PDF内容
        try:
            pdf_reader = PyPDF2.PdfReader(BytesIO(content))
            text_content = ""
            for page in pdf_reader.pages:
                text_content += page.extract_text() + "\n"

            if not text_content.strip():
                return {"status": "error", "message": "无法从PDF中提取文本内容"}

            # 使用LLM解析简历内容
            from src.libs.resume_and_cover_builder.llm.llm_job_parser import LLMParser
            llm_parser = LLMParser(api_key=api_key)
            resume_data = llm_parser.parse_resume_content(text_content)

            return {
                "status": "success",
                "message": "简历上传并解析成功",
                "resume_data": resume_data
            }

        except Exception as parse_error:
            return {"status": "error", "message": f"简历解析失败: {str(parse_error)}"}

    except Exception as e:
        return {"status": "error", "message": f"文件上传失败: {str(e)}"}

@app.post("/api/resume/generate")
async def generate_resume(request: ResumeGenerateRequest):
    job_url = request.job_url
    user_resume = request.user_resume

    if not job_url or not job_url.startswith('http'):
        return {"status": "error", "message": "无效的职位URL，请提供有效的网址"}

    try:
        # 检查组件初始化状态
        if not resume_facade.resume_generator or not resume_facade.style_manager:
            print("错误: 系统组件未正确初始化")
            return {"status": "error", "message": "系统组件未正确初始化，请检查系统配置"}

        # 验证API密钥
        is_valid, error_message = validate_api_key()
        if not is_valid:
            print(f"API密钥验证失败: {error_message}")
            return {"status": "warning", "message": f"API密钥问题: {error_message}，将使用降级模式解析"}

        # 如果用户上传了简历，设置用户简历数据
        if user_resume:
            print("检测到用户上传的简历，将生成针对性优化简历")
            resume_facade.set_user_resume(user_resume)

        # 调用原有逻辑
        print(f"开始处理职位URL: {job_url}")

        try:
            # 首先尝试使用Playwright抓取
            job_info_playwright = await scrape_job_with_playwright(job_url)

            if job_info_playwright and job_info_playwright.get('description'):
                print("使用Playwright成功抓取职位信息")
                # 将Playwright抓取的信息设置到resume_facade中
                from src.job import Job
                resume_facade.job = Job()
                resume_facade.job.role = job_info_playwright['role']
                resume_facade.job.company = job_info_playwright['company']
                resume_facade.job.location = job_info_playwright['location']
                resume_facade.job.description = job_info_playwright['description']
                resume_facade.job.link = job_url
            else:
                print("Playwright抓取失败，使用Selenium备用方案")
                resume_facade.link_to_job(job_url)

            # 检查是否成功解析到职位信息
            if hasattr(resume_facade, 'job') and resume_facade.job:
                job_info = {
                    "role": resume_facade.job.role or "未知职位",
                    "company": resume_facade.job.company or "未知公司",
                    "location": resume_facade.job.location or "未知地点",
                    "description": resume_facade.job.description or "职位描述信息不完整",
                    "url": resume_facade.job.link or job_url
                }

                # 检查是否使用了降级模式
                fallback_used = False
                if hasattr(resume_facade, 'llm_job_parser') and hasattr(resume_facade.llm_job_parser, 'fallback_mode'):
                    fallback_used = resume_facade.llm_job_parser.fallback_mode

                success_message = "职位信息已成功解析"
                if fallback_used:
                    success_message += "（使用降级模式）"

                print(f"成功解析职位信息: {job_info}")

                # 如果有用户简历数据，生成优化的简历内容
                optimized_resume = None
                if user_resume:
                    try:
                        optimized_resume = generate_optimized_resume_content(user_resume, job_info)
                        # 添加高亮标记到优化后的简历
                        optimized_resume = add_highlight_to_optimized_content(user_resume, optimized_resume)
                        print("成功生成优化简历内容并添加高亮标记")
                    except Exception as e:
                        print(f"简历优化失败: {str(e)}")

                return {
                    "status": "success",
                    "message": success_message,
                    "job_info": job_info,
                    "fallback_mode": fallback_used,
                    "optimized_resume": optimized_resume
                }
            else:
                return {"status": "error", "message": "无法解析职位信息，请检查URL是否有效"}

        except Exception as parse_error:
            print(f"职位解析错误: {str(parse_error)}")
            # 尝试提供基本的错误恢复
            return {
                "status": "error",
                "message": f"职位解析失败: {str(parse_error)}。可能的原因：网络连接问题、页面结构变化或API服务不可用。"
            }

    except AttributeError as e:
        error_msg = f"组件属性错误: {str(e)}"
        print(f"错误: {error_msg}")
        return {"status": "error", "message": error_msg}
    except ConnectionError as e:
        error_msg = f"网络连接错误: {str(e)}"
        print(f"错误: {error_msg}")
        return {"status": "error", "message": f"网络连接失败: {error_msg}。请检查网络连接或稍后重试。"}
    except Exception as e:
        error_msg = f"处理过程中发生错误: {str(e)}"
        print(f"错误: {error_msg}")
        return {"status": "error", "message": f"系统错误: {error_msg}。请稍后重试或联系技术支持。"}

@app.post("/api/cover-letter/generate")
async def generate_cover_letter(request: CoverLetterGenerateRequest):
    """
    生成求职信
    Args:
        request: 包含job_info和optimized_resume_html的请求数据
    """
    try:
        job_info = request.job_info
        optimized_resume_html = request.optimized_resume_html

        # 验证API密钥
        is_valid, error_message = validate_api_key()
        if not is_valid:
            return {"status": "error", "message": f"API密钥问题: {error_message}"}

        # 生成求职信
        cover_letter_html = generate_cover_letter_content(job_info, optimized_resume_html)

        return {
            "status": "success",
            "cover_letter_html": cover_letter_html,
            "message": "求职信生成成功"
        }

    except Exception as e:
        error_msg = f"求职信生成失败: {str(e)}"
        print(f"错误: {error_msg}")
        return {"status": "error", "message": error_msg}



@app.post("/api/resume/generate-pdf")
async def generate_resume_pdf(request: dict):
    """
    生成PDF简历
    Args:
        request: 包含optimized_resume_html的请求数据
    Returns:
        PDF文件的base64编码
    """
    try:
        optimized_resume_html = request.get('optimized_resume_html')

        if optimized_resume_html:
            # 使用优化后的HTML内容生成PDF
            return await generate_pdf_from_html(optimized_resume_html)
        else:
            return {"status": "error", "message": "缺少简历HTML内容"}

    except Exception as e:
        error_msg = f"PDF生成失败: {str(e)}"
        print(f"错误: {error_msg}")
        return {"status": "error", "message": error_msg}

async def scrape_job_with_playwright(job_url: str) -> dict:
    """使用Playwright抓取职位信息"""
    try:
        from playwright.async_api import async_playwright

        async with async_playwright() as p:
            browser = await p.chromium.launch(headless=True)
            page = await browser.new_page()

            # 设置用户代理
            await page.set_extra_http_headers({
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
            })

            await page.goto(job_url, wait_until='networkidle')
            await page.wait_for_timeout(3000)  # 等待页面完全加载

            # 获取页面内容
            content = await page.content()

            # 尝试提取职位信息
            job_info = {}

            try:
                # 提取职位标题
                title_element = await page.query_selector('h1')
                if title_element:
                    job_info['role'] = await title_element.inner_text()

                # 提取公司名称
                company_selectors = [
                    '.topcard__org-name-link',
                    '.job-details-jobs-unified-top-card__company-name',
                    '[data-test-id="job-details-company-name"]',
                    '.topcard__flavor--black-link'
                ]
                for selector in company_selectors:
                    company_element = await page.query_selector(selector)
                    if company_element:
                        job_info['company'] = await company_element.inner_text()
                        break

                # 提取地点
                location_selectors = [
                    '.topcard__flavor--bullet',
                    '.job-details-jobs-unified-top-card__bullet',
                    '[data-test-id="job-details-location"]'
                ]
                for selector in location_selectors:
                    location_element = await page.query_selector(selector)
                    if location_element:
                        location_text = await location_element.inner_text()
                        if '·' not in location_text and 'ago' not in location_text.lower():
                            job_info['location'] = location_text
                            break

                # 提取职位描述
                desc_selectors = [
                    '.description__text',
                    '.jobs-description-content__text',
                    '.jobs-box__html-content',
                    '[data-test-id="job-details-description"]'
                ]
                for selector in desc_selectors:
                    desc_element = await page.query_selector(selector)
                    if desc_element:
                        job_info['description'] = await desc_element.inner_text()
                        break

            except Exception as e:
                print(f"Playwright提取信息时出错: {e}")

            await browser.close()

            # 如果Playwright提取失败，使用原有的HTML内容进行LLM解析
            if not job_info.get('description'):
                from src.libs.resume_and_cover_builder.llm.llm_job_parser import LLMParser
                llm_parser = LLMParser(api_key=global_config.API_KEY)
                llm_parser.set_body_html(content)

                job_info['role'] = job_info.get('role') or llm_parser.extract_role()
                job_info['company'] = job_info.get('company') or llm_parser.extract_company_name()
                job_info['location'] = job_info.get('location') or llm_parser.extract_location()
                job_info['description'] = llm_parser.extract_job_description()

            job_info['url'] = job_url
            return job_info

    except Exception as e:
        print(f"Playwright抓取失败，回退到Selenium: {e}")
        return None

def add_highlight_to_optimized_content(original_resume, optimized_html):
    """
    为优化后的简历内容添加高亮标记
    """
    try:
        if not original_resume or not optimized_html:
            return optimized_html

        # 首先移除已存在的高亮切换按钮和脚本，避免重复
        import re
        optimized_html = re.sub(r'<div class="highlight-toggle".*?</div>', '', optimized_html, flags=re.DOTALL)
        optimized_html = re.sub(r'<script>.*?toggleHighlight.*?</script>', '', optimized_html, flags=re.DOTALL)

        # 添加高亮切换按钮和脚本
        toggle_button = '''
        <div class="highlight-toggle">
            <button onclick="toggleHighlight()" id="highlightToggleBtn">
                💡 隐藏高亮
            </button>
        </div>
        <script>
        let highlightVisible = true;

        function toggleHighlight() {
            const elements = document.querySelectorAll('.ai-optimized');
            const button = document.getElementById('highlightToggleBtn');

            if (highlightVisible) {
                // 隐藏高亮 - 添加hidden类
                elements.forEach(el => {
                    el.classList.add('ai-optimized-hidden');
                });
            } else {
                // 显示高亮 - 移除hidden类
                elements.forEach(el => {
                    el.classList.remove('ai-optimized-hidden');
                });
            }

            highlightVisible = !highlightVisible;
            button.innerHTML = highlightVisible ? '💡 隐藏高亮' : '✨ 显示高亮';
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            const elements = document.querySelectorAll('.ai-optimized');
            if (elements.length > 0) {
                console.log('找到 ' + elements.length + ' 个AI优化标记');
            }
        });
        </script>
        '''

        # 在body结束标签前插入切换按钮
        highlighted_html = optimized_html
        if '</body>' in highlighted_html:
            highlighted_html = highlighted_html.replace('</body>', toggle_button + '</body>')

        return highlighted_html

    except Exception as e:
        print(f"添加高亮标记时出错: {str(e)}")
        return optimized_html

def remove_highlight_for_pdf(html_content):
    """
    移除PDF生成时的高亮样式和切换按钮
    """
    try:
        import re

        # 移除切换按钮div
        html_content = re.sub(r'<div class="highlight-toggle".*?</div>', '', html_content, flags=re.DOTALL)

        # 移除脚本
        html_content = re.sub(r'<script>.*?</script>', '', html_content, flags=re.DOTALL)

        # 移除ai-optimized类，但保留内容
        html_content = re.sub(r'<([^>]+)\s+class="[^"]*ai-optimized[^"]*"([^>]*)>', r'<\1\2>', html_content)
        html_content = re.sub(r'class="[^"]*ai-optimized[^"]*"\s*', '', html_content)

        return html_content

    except Exception as e:
        print(f"移除高亮样式时出错: {str(e)}")
        return html_content





# 已移除capture_stats_section_as_image_async函数，不再需要截图替换

# 已移除capture_stats_section_as_image函数，不再需要截图替换





# 已删除create_fallback_stats_screenshot函数，不再使用Chrome WebDriver备选方案

# 已删除generate_pdf_with_browser_print函数，不再使用Chrome WebDriver生成PDF

async def generate_pdf_from_html(html_content):
    """
    简历PDF生成函数 - 仅用于简历生成
    """
    try:
        from src.utils.chrome_utils import HTML_to_PDF

        print("开始使用Chrome CDP API生成简历PDF...")

        # 清理HTML内容，提取实际的HTML代码
        if '```html' in html_content:
            # 提取HTML代码块
            start = html_content.find('```html') + 7
            end = html_content.find('```', start)
            if end != -1:
                html_content = html_content[start:end].strip()

        # 移除高亮样式，确保PDF中不显示高亮
        html_content = remove_highlight_for_pdf(html_content)

        # 使用全局WebDriver实例生成PDF
        global driver
        if driver is None:
            raise RuntimeError("WebDriver未初始化")

        print("使用全局WebDriver实例生成简历PDF...")

        # 调用简历生成的PDF函数
        pdf_base64 = HTML_to_PDF(html_content, driver)

        print(f"简历PDF生成成功，base64长度: {len(pdf_base64)}")

        return {
            "status": "success",
            "pdf_data": pdf_base64,
            "message": "简历PDF生成成功"
        }

    except Exception as e:
        print(f"简历PDF生成失败: {str(e)}")
        return {
            "status": "error",
            "message": f"生成简历PDF失败: {str(e)}"
        }







if __name__ == "__main__":
    import uvicorn
    print("启动 FastAPI 服务器...")
    print("LinkedIn API 可在 http://localhost:8003/api/linkedin/ 访问")
    print("简历生成 API 可在 http://localhost:8003/api/resume/ 访问")
    print("健康检查 API 可在 http://localhost:8003/api/health 访问")
    uvicorn.run(app, host="0.0.0.0", port=8003)